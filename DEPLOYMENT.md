# 新功能部署说明

## 🎯 重构完成

已完全移除老版本的语音识别和翻译代码，只保留基于OpenAI API的新实现。

## 📂 新增云函数

需要在uniCloud控制台部署以下新云函数：

1. **speech-recognition-whisper**
   - 基于OpenAI Whisper-large-v3的语音识别
   - 路径：`uniCloud-aliyun/cloudfunctions/speech-recognition-whisper/`

2. **subtitle-translation-gpt**
   - 基于GPT-3.5-turbo的高质量翻译
   - 路径：`uniCloud-aliyun/cloudfunctions/subtitle-translation-gpt/`

## 🔧 修改的云函数

需要重新部署以下修改过的云函数：

1. **process-video-task**
   - 移除了老版本的语音识别和翻译代码
   - 现在直接使用新功能

2. **poll-mps-tasks**
   - 移除了智能调度器调用
   - 直接使用新的语音识别功能

## ⚙️ 配置更新

新增OpenAI API配置：
- 路径：`src/uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center/openai-api/config.json`
- 包含API密钥和模型配置

## 🗑️ 已清理的内容

- ❌ 删除了智能调度器云函数
- ❌ 删除了阿里云DashScope配置（Paraformer）
- ❌ 删除了阿里云机器翻译配置
- ❌ 删除了参考文件和测试脚本

## 🚀 部署步骤

1. **部署新云函数**
   ```bash
   # 在uniCloud控制台上传这两个新函数
   speech-recognition-whisper/
   subtitle-translation-gpt/
   ```

2. **更新现有云函数**
   ```bash
   # 重新部署修改过的函数
   process-video-task/
   poll-mps-tasks/
   ```

3. **验证配置**
   - 确保OpenAI API配置正确
   - 测试新功能是否正常工作

## 🎉 完成

- ✅ 所有老版本代码已移除
- ✅ 流程简化，直接使用新功能
- ✅ 保持了完整的功能性
- ✅ 提升了代码质量和维护性

现在系统将统一使用Whisper进行语音识别，使用GPT进行翻译，提供更高质量的结果。