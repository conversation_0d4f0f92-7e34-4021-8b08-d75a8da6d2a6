// uniCloud云函数：基于GPT的高质量字幕翻译
// 参考 audio-subtitle-serverless.js 的翻译最佳实践进行优化
"use strict";

const createConfig = require("uni-config-center");

// 常量配置
const CONFIG = {
  DEFAULT_MODEL: "gpt-3.5-turbo",
  TEMPERATURE: 0.3,
  MAX_TOKENS: 4000,
  API_TIMEOUT: 120000, // 2分钟超时
  BATCH_SIZE: 50, // 批量翻译条目数量限制
};

// 语言映射表 - 参考 audio-subtitle-serverless.js
const LANGUAGE_MAP = {
  'zh': '中文',
  'ja': '日文',
  'ko': '韩文',
  'fr': '法文',
  'de': '德文',
  'es': '西班牙文',
  'it': '意大利文',
  'pt': '葡萄牙文',
  'ru': '俄文',
  'ar': '阿拉伯文',
  'hi': '印地文',
  'th': '泰文',
  'vi': '越南文',
  'tr': '土耳其文',
  'pl': '波兰文',
  'nl': '荷兰文',
  'sv': '瑞典文',
  'da': '丹麦文',
  'no': '挪威文',
  'fi': '芬兰文'
};

/**
 * 使用GPT进行高质量字幕翻译
 * 参考 audio-subtitle-serverless.js 的批量翻译优化实现
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID
 * @param {string} event.sourceLanguage - 源语言代码，默认"auto"
 * @param {string} event.targetLanguage - 目标语言代码，默认"zh"
 * @returns {Object} 翻译结果
 */
exports.main = async (event, context) => {
  const startTime = Date.now();

  try {
    const { taskId, sourceLanguage = "auto", targetLanguage = "zh" } = event;

    console.log("🔄 subtitle-translation-gpt 云函数启动");
    console.log("📥 输入参数：", {
      taskId,
      sourceLanguage,
      targetLanguage,
      timestamp: new Date().toISOString()
    });

    // 参数验证
    if (!taskId) {
      return createErrorResponse(400, "缺少必要参数：taskId");
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 验证和获取任务信息
    const { task, actualSourceLanguage } = await validateAndGetTask(
      tasksCollection,
      taskId,
      sourceLanguage
    );

    // 检查是否需要翻译
    if (actualSourceLanguage === targetLanguage) {
      console.log("🔄 源语言和目标语言相同，跳过翻译");

      await tasksCollection.doc(taskId).update({
        status: "merging",
        updateTime: new Date(),
      });

      const processingTime = (Date.now() - startTime) / 1000;
      return createSuccessResponse("语言相同无需翻译，直接进入字幕烧录", {
        taskId,
        status: "skipped",
        reason: "same_language",
        processingTime
      });
    }

    console.log(`🔄 开始翻译流程：${actualSourceLanguage} -> ${targetLanguage}`);

    // 获取和验证API配置
    const { apiKey, baseUrl, model } = await getAndValidateGptConfig();

    console.log("✅ GPT配置验证通过", {
      baseUrl,
      model,
      hasApiKey: !!apiKey
    });

    try {
      // 执行翻译流程
      console.log("📥 开始下载字幕文件...");
      const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

      console.log("📝 解析SRT字幕文件...");
      const subtitleEntries = parseSRT(srtContent);

      if (subtitleEntries.length === 0) {
        throw new Error("字幕文件为空或格式错误");
      }

      console.log(`📊 解析完成，共 ${subtitleEntries.length} 条字幕`);

      // 执行批量翻译
      console.log("🔄 开始批量翻译...");
      const translatedEntries = await translateSubtitlesBatchOptimized(
        subtitleEntries,
        apiKey,
        baseUrl,
        model,
        actualSourceLanguage,
        targetLanguage
      );

      console.log(`✅ 翻译完成，共处理 ${translatedEntries.length} 条字幕`);

      // 生成和上传翻译后的SRT
      console.log("📤 生成并上传翻译后的字幕文件...");
      const translatedSrtContent = generateSRT(translatedEntries);
      const uploadResult = await uploadTranslatedSrtToOSS(taskId, translatedSrtContent);

      // 更新任务状态
      await tasksCollection.doc(taskId).update({
        status: "merging",
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        updateTime: new Date(),
      });

      const processingTime = (Date.now() - startTime) / 1000;
      console.log(`🎉 字幕翻译任务完成，耗时: ${processingTime.toFixed(2)}秒`);

      return createSuccessResponse("字幕翻译成功", {
        taskId,
        status: "completed",
        translatedCount: translatedEntries.length,
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        processingTime,
        sourceLanguage: actualSourceLanguage,
        targetLanguage
      });

    } catch (error) {
      console.error("❌ 翻译处理失败：", error);

      // 更新任务状态为失败
      try {
        await tasksCollection.doc(taskId).update({
          status: "failed",
          errorMessage: "翻译失败：" + error.message,
          updateTime: new Date(),
        });
      } catch (updateError) {
        console.error("⚠️ 更新任务状态失败：", updateError);
      }

      throw error;
    }

  } catch (error) {
    const processingTime = (Date.now() - startTime) / 1000;
    console.error("❌ subtitle-translation-gpt 云函数执行错误：", {
      error: error.message,
      stack: error.stack,
      processingTime: `${processingTime.toFixed(2)}秒`,
      timestamp: new Date().toISOString()
    });

    return createErrorResponse(500, "字幕翻译失败: " + error.message, {
      processingTime,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 创建成功响应
 * @param {string} message - 成功消息
 * @param {Object} data - 响应数据
 * @returns {Object} 标准化成功响应
 */
function createSuccessResponse(message, data) {
  return {
    code: 200,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

/**
 * 创建错误响应
 * @param {number} code - 错误代码
 * @param {string} message - 错误消息
 * @param {Object} extra - 额外信息
 * @returns {Object} 标准化错误响应
 */
function createErrorResponse(code, message, extra = {}) {
  return {
    code,
    message,
    timestamp: new Date().toISOString(),
    ...extra
  };
}

/**
 * 验证和获取任务信息
 * @param {Object} tasksCollection - 任务集合
 * @param {string} taskId - 任务ID
 * @param {string} sourceLanguage - 源语言
 * @returns {Promise<Object>} 任务信息和实际源语言
 */
async function validateAndGetTask(tasksCollection, taskId, sourceLanguage) {
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data || taskInfo.data.length === 0) {
    throw new Error("任务不存在");
  }

  const task = taskInfo.data[0];
  if (!task.subtitleOssUrl) {
    throw new Error("缺少字幕文件地址");
  }

  // 确定实际源语言
  const actualSourceLanguage = sourceLanguage === "auto" ?
    (task.recognizedLanguage || "en") : sourceLanguage;

  return { task, actualSourceLanguage };
}

/**
 * 获取和验证GPT API配置
 * @returns {Promise<Object>} API配置
 */
async function getAndValidateGptConfig() {
  const gptConfig = createConfig({
    pluginId: "openai-api",
    defaultConfig: {
      baseUrl: "https://aihubmix.com",
      model: CONFIG.DEFAULT_MODEL,
    },
  });

  const apiKey = gptConfig.config("apiKey");
  const baseUrl = gptConfig.config("baseUrl");
  const model = gptConfig.config("model");

  if (!apiKey) {
    throw new Error("GPT API配置缺失，请检查apiKey");
  }

  return { apiKey, baseUrl, model };
}

/**
 * 从OSS下载SRT文件内容
 * @param {string} ossUrl - OSS文件地址
 * @returns {Promise<string>} SRT文件内容
 */
async function downloadSrtFromOSS(ossUrl) {
  try {
    console.log("从OSS下载SRT文件：", ossUrl);
    
    const response = await uniCloud.httpclient.request(ossUrl, {
      method: 'GET',
      timeout: 30000,
    });

    if (response.status !== 200) {
      throw new Error(`下载SRT文件失败，状态码: ${response.status}`);
    }

    return response.data;
  } catch (error) {
    console.error("下载SRT文件失败：", error);
    throw new Error(`下载SRT文件失败: ${error.message}`);
  }
}

/**
 * 解析SRT字幕格式
 * @param {string} srtContent - SRT字幕内容
 * @returns {Array} 解析后的字幕条目数组
 */
function parseSRT(srtContent) {
  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n");

      if (text.trim()) {
        entries.push({
          index,
          timeRange,
          text: text.trim(),
        });
      }
    }
  }

  return entries;
}

/**
 * 优化的批量翻译字幕条目
 * 参考 audio-subtitle-serverless.js 的翻译最佳实践
 * @param {Array} entries - 字幕条目数组
 * @param {string} apiKey - API密钥
 * @param {string} baseUrl - API基础URL
 * @param {string} model - 模型名称
 * @param {string} sourceLanguage - 源语言
 * @param {string} targetLanguage - 目标语言
 * @returns {Promise<Array>} 翻译后的字幕条目数组
 */
async function translateSubtitlesBatchOptimized(entries, apiKey, baseUrl, model, sourceLanguage, targetLanguage) {
  const translationStartTime = Date.now();
  console.log("🔄 开始批量翻译字幕", {
    totalEntries: entries.length,
    sourceLanguage,
    targetLanguage,
    model
  });

  // 过滤和编号有效文本
  const validEntries = [];
  const originalTexts = [];

  entries.forEach((entry, index) => {
    if (entry.text?.trim()) {
      validEntries.push({ ...entry, originalIndex: index });
      originalTexts.push(`${validEntries.length}. ${entry.text.trim()}`);
    }
  });

  if (validEntries.length === 0) {
    console.log("⚠️ 没有有效的字幕文本需要翻译");
    return entries;
  }

  // 合并文本进行批量翻译
  const combinedText = originalTexts.join("\n");
  console.log(`📝 准备翻译 ${validEntries.length} 条字幕，文本长度：${combinedText.length}`);

  // 获取目标语言名称
  const targetLangName = LANGUAGE_MAP[targetLanguage] || targetLanguage.toUpperCase();

  // 构建优化的翻译请求 - 参考 audio-subtitle-serverless.js
  const requestBody = {
    model,
    messages: [
      {
        role: "system",
        content: `你是一个专业的多语言翻译专家。请将用户提供的编号文本逐行翻译成自然流畅的${targetLangName}，保持原意不变。请保持相同的编号格式，每行一个翻译结果。`
      },
      {
        role: "user",
        content: `请将以下编号的文本翻译成${targetLangName}，保持编号格式：\n\n${combinedText}`
      }
    ],
    temperature: CONFIG.TEMPERATURE,
    max_tokens: CONFIG.MAX_TOKENS
  };

  console.log("📡 发送GPT翻译请求", {
    model,
    temperature: CONFIG.TEMPERATURE,
    maxTokens: CONFIG.MAX_TOKENS,
    textLength: combinedText.length
  });

  // 调用GPT API
  const apiStartTime = Date.now();
  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    data: requestBody,
    timeout: CONFIG.API_TIMEOUT,
  });

  const apiTime = (Date.now() - apiStartTime) / 1000;
  console.log(`📡 GPT API响应完成，耗时: ${apiTime.toFixed(2)}秒`);

  if (response.status !== 200) {
    console.error("❌ GPT API请求失败", {
      status: response.status,
      response: response.data
    });
    throw new Error(`GPT API请求失败，状态码: ${response.status}`);
  }

  const result = response.data;

  if (!result.choices?.length) {
    throw new Error("GPT API返回空结果");
  }

  const translatedText = result.choices[0].message.content.trim();
  console.log(`✅ GPT翻译完成，返回长度: ${translatedText.length}`);

  // 解析翻译结果 - 优化匹配逻辑
  const translatedLines = translatedText.split('\n').map(line => line.trim()).filter(line => line);
  const translatedEntries = [...entries]; // 复制原数组

  console.log(`📝 解析翻译结果，共 ${translatedLines.length} 行`);

  // 为每个有效条目匹配翻译结果
  validEntries.forEach((validEntry, validIndex) => {
    const targetPattern = `${validIndex + 1}.`;
    let translatedLine = null;

    // 查找对应的翻译
    for (const line of translatedLines) {
      if (line.startsWith(targetPattern)) {
        translatedLine = line.substring(targetPattern.length).trim();
        break;
      }
    }

    if (translatedLine) {
      // 更新原始位置的条目
      translatedEntries[validEntry.originalIndex] = {
        ...validEntry,
        text: translatedLine
      };
      console.log(`✓ 第${validIndex + 1}条翻译完成`);
    } else {
      // 翻译失败，保留原文并记录警告
      console.warn(`⚠️ 第${validIndex + 1}条未找到翻译，保留原文: "${validEntry.text.substring(0, 50)}..."`);
    }
  });

  const totalTime = (Date.now() - translationStartTime) / 1000;
  console.log(`🎯 批量翻译完成`, {
    totalEntries: entries.length,
    validEntries: validEntries.length,
    translatedLines: translatedLines.length,
    processingTime: `${totalTime.toFixed(2)}秒`
  });

  return translatedEntries;
}

/**
 * 生成SRT字幕格式
 * @param {Array} entries - 字幕条目数组
 * @returns {string} SRT格式字符串
 */
function generateSRT(entries) {
  return entries
    .map((entry) => {
      return `${entry.index}\n${entry.timeRange}\n${entry.text}\n`;
    })
    .join("\n");
}

/**
 * 上传翻译后的SRT文件到OSS
 * @param {string} taskId - 任务ID
 * @param {string} srtContent - 翻译后的SRT内容
 * @returns {Promise<Object>} 上传结果
 */
async function uploadTranslatedSrtToOSS(taskId, srtContent) {
  const OSS = require("ali-oss");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成翻译后SRT文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.srt`;

    // 上传翻译后的SRT内容到OSS
    const uploadResult = await client.put(objectKey, Buffer.from(srtContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传翻译后SRT文件完成，地址：", subtitleOssUrl);

    return {
      subtitleOssUrl: subtitleOssUrl,
      objectKey: objectKey,
    };

  } catch (error) {
    console.error("上传翻译后SRT文件失败：", error);
    throw new Error("上传翻译后字幕文件失败：" + error.message);
  }
}