// uniCloud云函数：基于GPT的高质量字幕翻译
"use strict";

const createConfig = require("uni-config-center");

/**
 * 使用GPT进行高质量字幕翻译
 * 参考simple_subtitle_demo.py的批量翻译实现
 * 
 * @param {Object} event
 * @param {string} event.taskId - 任务ID
 * @param {string} event.sourceLanguage - 源语言代码，默认"auto"
 * @param {string} event.targetLanguage - 目标语言代码，默认"zh"
 * @returns {Object} 翻译结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, sourceLanguage = "auto", targetLanguage = "zh" } = event;

    console.log("subtitle-translation-gpt 云函数被调用，参数：", { 
      taskId, 
      sourceLanguage, 
      targetLanguage 
    });

    // 参数验证
    if (!taskId) {
      return {
        code: 400,
        message: "缺少必要参数：taskId",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 获取任务信息
    const taskInfo = await tasksCollection.doc(taskId).get();
    let task;
    
    if (!taskInfo.data || taskInfo.data.length === 0) {
      // 如果是测试任务ID，使用默认测试数据
      if (taskId.startsWith("test-")) {
        console.log("检测到测试任务ID，使用默认测试数据");
        // 创建一个模拟的测试SRT文件用于测试翻译功能
        const testSrtUrl = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/subtitle/test_subtitle.srt";
        task = {
          subtitleOssUrl: testSrtUrl,
          sourceLanguage: sourceLanguage,
          targetLanguage: targetLanguage
        };
      } else {
        throw new Error("任务不存在");
      }
    } else {
      task = taskInfo.data[0];
    }
    const { subtitleOssUrl, recognizedLanguage } = task;

    if (!subtitleOssUrl) {
      throw new Error("缺少字幕文件地址");
    }

    // 如果源语言是auto，使用识别到的语言
    const actualSourceLanguage = sourceLanguage === "auto" ? 
      (recognizedLanguage || "en") : sourceLanguage;

    // 如果源语言和目标语言相同，跳过翻译
    if (actualSourceLanguage === targetLanguage) {
      console.log("源语言和目标语言相同，跳过翻译");
      
      // 直接进入字幕烧录阶段
      await tasksCollection.doc(taskId).update({
        status: "merging",
        updateTime: new Date(),
      });

      return {
        code: 200,
        message: "语言相同无需翻译，直接进入字幕烧录",
        data: {
          taskId: taskId,
          status: "skipped",
          reason: "same_language"
        },
      };
    }

    console.log(`开始翻译：${actualSourceLanguage} -> ${targetLanguage}`);

    // 获取GPT API配置
    const gptConfig = createConfig({
      pluginId: "openai-api",
      defaultConfig: {
        baseUrl: "https://aihubmix.com",
        model: "gpt-3.5-turbo",
      },
    });

    const apiKey = gptConfig.config("apiKey");
    const baseUrl = gptConfig.config("baseUrl");
    const model = gptConfig.config("model");

    // 验证必需的配置
    if (!apiKey) {
      throw new Error("GPT API配置缺失，请检查apiKey");
    }

    console.log("GPT配置检查通过，baseUrl：", baseUrl, "，model：", model);

    try {
      // 从OSS下载字幕文件内容
      const srtContent = await downloadSrtFromOSS(subtitleOssUrl);
      if (!srtContent) {
        throw new Error("无法下载字幕文件");
      }

      // 解析SRT字幕
      const subtitleEntries = parseSRT(srtContent);
      console.log(`解析到 ${subtitleEntries.length} 条字幕`);

      if (subtitleEntries.length === 0) {
        throw new Error("字幕文件为空或格式错误");
      }

      // 使用GPT批量翻译
      const translatedEntries = await translateSubtitlesBatch(
        subtitleEntries,
        apiKey,
        baseUrl,
        model,
        actualSourceLanguage,
        targetLanguage
      );

      console.log(`翻译完成，共处理 ${translatedEntries.length} 条字幕`);

      // 生成翻译后的SRT内容
      const translatedSrtContent = generateSRT(translatedEntries);

      // 上传翻译后的SRT文件到OSS
      const uploadResult = await uploadTranslatedSrtToOSS(taskId, translatedSrtContent);

      // 更新任务状态为merging，准备字幕烧录
      await tasksCollection.doc(taskId).update({
        status: "merging",
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        updateTime: new Date(),
      });

      return {
        code: 200,
        message: "字幕翻译成功",
        data: {
          taskId: taskId,
          status: "completed",
          translatedCount: translatedEntries.length,
          subtitleOssUrl: uploadResult.subtitleOssUrl,
        },
      };

    } catch (error) {
      console.error("翻译处理失败：", error);

      // 更新任务状态为失败
      await tasksCollection.doc(taskId).update({
        status: "failed",
        errorMessage: "翻译失败：" + error.message,
        updateTime: new Date(),
      });

      throw error;
    }

  } catch (error) {
    console.error("subtitle-translation-gpt 云函数执行错误：", error);

    return {
      code: 500,
      message: "字幕翻译失败: " + error.message,
    };
  }
};

/**
 * 从OSS下载SRT文件内容
 * @param {string} ossUrl - OSS文件地址
 * @returns {Promise<string>} SRT文件内容
 */
async function downloadSrtFromOSS(ossUrl) {
  try {
    console.log("从OSS下载SRT文件：", ossUrl);
    
    const response = await uniCloud.httpclient.request(ossUrl, {
      method: 'GET',
      timeout: 30000,
    });

    if (response.status !== 200) {
      throw new Error(`下载SRT文件失败，状态码: ${response.status}`);
    }

    return response.data;
  } catch (error) {
    console.error("下载SRT文件失败：", error);
    throw new Error(`下载SRT文件失败: ${error.message}`);
  }
}

/**
 * 解析SRT字幕格式
 * @param {string} srtContent - SRT字幕内容
 * @returns {Array} 解析后的字幕条目数组
 */
function parseSRT(srtContent) {
  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n");

      if (text.trim()) {
        entries.push({
          index,
          timeRange,
          text: text.trim(),
        });
      }
    }
  }

  return entries;
}

/**
 * 批量翻译字幕条目
 * 参考simple_subtitle_demo.py的translate_segments_batch实现
 * @param {Array} entries - 字幕条目数组
 * @param {string} apiKey - API密钥
 * @param {string} baseUrl - API基础URL
 * @param {string} model - 模型名称
 * @param {string} sourceLanguage - 源语言
 * @param {string} targetLanguage - 目标语言
 * @returns {Promise<Array>} 翻译后的字幕条目数组
 */
async function translateSubtitlesBatch(entries, apiKey, baseUrl, model, sourceLanguage, targetLanguage) {
  console.log("开始批量翻译字幕");

  // 构建包含所有文本的翻译请求
  const originalTexts = [];
  entries.forEach((entry, index) => {
    if (entry.text.trim()) {
      originalTexts.push(`${index + 1}. ${entry.text.trim()}`);
    }
  });

  if (originalTexts.length === 0) {
    return entries;
  }

  // 合并所有文本进行一次性翻译
  const combinedText = originalTexts.join("\n");
  console.log(`合并文本长度：${combinedText.length}，准备翻译`);

  // 语言映射
  const languageNames = {
    "zh": "中文", "ja": "日文", "ko": "韩文", "fr": "法文", "de": "德文",
    "es": "西班牙文", "it": "意大利文", "pt": "葡萄牙文", "ru": "俄文",
    "ar": "阿拉伯文", "hi": "印地文", "th": "泰文", "vi": "越南文",
    "tr": "土耳其文", "pl": "波兰文", "nl": "荷兰文", "sv": "瑞典文",
    "da": "丹麦文", "no": "挪威文", "fi": "芬兰文"
  };
  
  const targetLangName = languageNames[targetLanguage] || targetLanguage.toUpperCase();

  const requestBody = {
    model: model,
    messages: [
      {
        role: "system",
        content: `你是一个专业的多语言翻译专家。请将用户提供的编号文本逐行翻译成自然流畅的${targetLangName}，保持原意不变。请保持相同的编号格式，每行一个翻译结果。`
      },
      {
        role: "user",
        content: `请将以下编号的文本翻译成${targetLangName}，保持编号格式：\n\n${combinedText}`
      }
    ],
    temperature: 0.3,
    max_tokens: 4000
  };

  console.log("发送GPT翻译请求...");

  // 调用GPT API
  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    data: requestBody,
    timeout: 120000, // 2分钟超时
  });

  if (response.status !== 200) {
    console.error("GPT API请求失败，状态码：", response.status);
    console.error("响应内容：", response.data);
    throw new Error(`GPT API请求失败，状态码: ${response.status}`);
  }

  const result = response.data;
  
  if (!result.choices || result.choices.length === 0) {
    throw new Error("GPT API返回空结果");
  }

  const translatedText = result.choices[0].message.content.trim();
  console.log("GPT翻译完成，返回长度：", translatedText.length);

  // 解析翻译结果
  const translatedLines = translatedText.split('\n');
  const translatedEntries = [];

  entries.forEach((entry, index) => {
    if (entry.text.trim()) {
      // 查找对应的翻译
      let translatedLine = null;
      const targetPattern = `${index + 1}.`;
      
      for (const line of translatedLines) {
        if (line.trim().startsWith(targetPattern)) {
          translatedLine = line.trim().substring(targetPattern.length).trim();
          break;
        }
      }

      if (translatedLine) {
        translatedEntries.push({
          ...entry,
          text: translatedLine
        });
        console.log(`✓ 第${index + 1}条翻译完成`);
      } else {
        // 如果没找到翻译，保留原文
        console.warn(`⚠ 第${index + 1}条未找到翻译，保留原文`);
        translatedEntries.push(entry);
      }
    } else {
      translatedEntries.push(entry);
    }
  });

  return translatedEntries;
}

/**
 * 生成SRT字幕格式
 * @param {Array} entries - 字幕条目数组
 * @returns {string} SRT格式字符串
 */
function generateSRT(entries) {
  return entries
    .map((entry) => {
      return `${entry.index}\n${entry.timeRange}\n${entry.text}\n`;
    })
    .join("\n");
}

/**
 * 上传翻译后的SRT文件到OSS
 * @param {string} taskId - 任务ID
 * @param {string} srtContent - 翻译后的SRT内容
 * @returns {Promise<Object>} 上传结果
 */
async function uploadTranslatedSrtToOSS(taskId, srtContent) {
  const OSS = require("ali-oss");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成翻译后SRT文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.srt`;

    // 上传翻译后的SRT内容到OSS
    const uploadResult = await client.put(objectKey, Buffer.from(srtContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传翻译后SRT文件完成，地址：", subtitleOssUrl);

    return {
      subtitleOssUrl: subtitleOssUrl,
      objectKey: objectKey,
    };

  } catch (error) {
    console.error("上传翻译后SRT文件失败：", error);
    throw new Error("上传翻译后字幕文件失败：" + error.message);
  }
}