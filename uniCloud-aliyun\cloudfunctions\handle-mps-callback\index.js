// uniCloud云函数：处理阿里云MPS事件回调（已废弃，改为轮询模式）
// 此文件保留作为备份，实际功能已迁移到 poll-mps-tasks 云函数
"use strict";

const Core = require("@alicloud/pop-core");
const createConfig = require("uni-config-center");

/**
 * 处理阿里云MPS的事件回调
 *
 * @param {Object} event - 阿里云事件数据
 * @param {Object} context - 云函数上下文
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    console.log("handle-mps-callback 云函数被调用，事件数据：", JSON.stringify(event, null, 2));

    // 解析阿里云MPS回调数据
    let callbackData;

    // 检查是否有body字段（阿里云MPS回调格式）
    if (event.body) {
      try {
        // 解析body中的JSON字符串
        callbackData = JSON.parse(event.body);
        console.log("解析后的回调数据：", JSON.stringify(callbackData, null, 2));
      } catch (parseError) {
        console.error("解析body JSON失败：", parseError);
        return {
          code: 400,
          message: "回调数据格式错误：无法解析body JSON",
        };
      }
    } else {
      // 兼容旧格式（如果有的话）
      callbackData = event;
    }

    // 提取关键字段（阿里云MPS回调字段都是小写）
    const { type: eventType, jobId: taskJobId, state: taskState } = callbackData;

    if (!eventType || !taskJobId) {
      console.error("回调数据格式错误：缺少type或jobId字段");
      return {
        code: 400,
        message: "回调数据格式错误：缺少必要字段",
      };
    }

    console.log(
      "提取的关键信息 - 事件类型：",
      eventType,
      "，任务ID：",
      taskJobId,
      "，状态：",
      taskState
    );

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 根据事件类型处理不同的回调
    switch (eventType) {
      case "Transcode":
        // MPS转码完成事件（音频提取或字幕烧录完成）
        console.log("处理MPS转码事件");
        await handleTranscodeComplete(callbackData, tasksCollection);
        break;
      default:
        console.log("未处理的事件类型：", eventType);
        break;
    }

    return {
      code: 200,
      message: "事件处理成功",
    };
  } catch (error) {
    console.error("handle-mps-callback 云函数执行错误：", error);

    return {
      code: 500,
      message: "事件处理失败: " + error.message,
    };
  }
};

/**
 * 处理MPS转码完成事件
 * @param {Object} callbackData - 阿里云MPS回调数据
 * @param {Object} tasksCollection - 任务集合
 */
async function handleTranscodeComplete(callbackData, tasksCollection) {
  try {
    // 提取关键字段（阿里云MPS回调字段都是小写）
    const { jobId: taskJobId, state: taskState } = callbackData;

    console.log("转码任务回调，JobId：", taskJobId, "，状态：", taskState);

    // 通过jobId查询对应的任务记录
    console.log("通过jobId查询数据库：", taskJobId);

    const taskQuery = await tasksCollection
      .where({
        mpsJobId: taskJobId,
      })
      .get();

    if (!taskQuery.data || taskQuery.data.length === 0) {
      console.error("无法找到对应的任务记录，JobId：", taskJobId);
      return;
    }

    const taskId = taskQuery.data[0]._id;
    console.log("通过jobId查询到taskId：", taskId);

    // 获取任务当前状态，判断是音频提取还是字幕烧录
    const currentTask = taskQuery.data[0];
    const currentStatus = currentTask.status;

    console.log("当前任务状态：", currentStatus);

    // 处理转码结果
    if (taskState === "Success") {
      if (currentStatus === "extracting_audio") {
        // 音频提取完成
        console.log("音频提取成功，开始查询音频文件地址并启动语音识别");

        // 更新状态为音频提取完成
        await tasksCollection.doc(taskId).update({
          status: "audio_extracted",
          mpsJobId: taskJobId,
          updateTime: new Date(),
        });

        // 查询MPS任务结果获取音频文件地址
        const audioOssUrl = await queryMpsJobResult(taskJobId, 'audio');

        if (audioOssUrl) {
          // 更新任务状态为开始语音识别
          await tasksCollection.doc(taskId).update({
            status: "recognizing",
            audioOssUrl: audioOssUrl,
            updateTime: new Date(),
          });

          // 调用process-video-task云函数开始语音识别
          const processResult = await uniCloud.callFunction({
            name: "process-video-task",
            data: {
              taskId: taskId,
              audioOssUrl: audioOssUrl,
              action: "speech_recognition",
            },
          });

          console.log("调用process-video-task语音识别结果：", processResult);
        } else {
          console.error("无法获取音频文件地址");
          await tasksCollection.doc(taskId).update({
            status: "failed",
            errorMessage: "无法获取音频文件地址",
            updateTime: new Date(),
          });
        }
      } else if (currentStatus === "merging") {
        // 字幕烧录完成
        console.log("字幕烧录成功，查询最终视频文件地址");

        // 查询MPS任务结果获取最终视频文件地址
        const finalVideoUrl = await queryMpsJobResult(taskJobId, 'video');

        if (finalVideoUrl) {
          // 验证返回的URL是否为视频文件
          if (finalVideoUrl.includes('/audio/') || finalVideoUrl.endsWith('.mp3')) {
            console.error("获取到的是音频文件URL而不是视频文件URL，任务状态设为失败");
            await tasksCollection.doc(taskId).update({
              status: "failed",
              errorMessage: "获取到错误的文件类型：音频而非视频",
              updateTime: new Date(),
            });
          } else {
            // 更新任务状态为完成
            await tasksCollection.doc(taskId).update({
              status: "completed",
              finalVideoUrl: finalVideoUrl,
              updateTime: new Date(),
            });

            console.log("字幕烧录任务完成，最终视频地址：", finalVideoUrl);
          }
        } else {
          console.error("无法获取最终视频文件地址");
          await tasksCollection.doc(taskId).update({
            status: "failed",
            errorMessage: "无法获取最终视频文件地址",
            updateTime: new Date(),
          });
        }
      } else {
        console.warn("未知的任务状态，无法处理转码完成事件：", currentStatus);
      }
    } else {
      // 转码失败
      console.error("转码失败，JobId：", taskJobId, "，状态：", taskState);

      const errorMessage = currentStatus === "extracting_audio"
        ? `音频提取失败：${taskState}`
        : `字幕烧录失败：${taskState}`;

      await tasksCollection.doc(taskId).update({
        status: "failed",
        errorMessage: errorMessage,
        updateTime: new Date(),
      });
    }
  } catch (error) {
    console.error("处理转码完成事件失败：", error);
    throw error;
  }
}

/**
 * 查询MPS任务结果获取输出文件地址
 * @param {string} jobId - MPS任务ID
 * @param {string} taskType - 任务类型：'audio' 为音频提取，'video' 为视频合成
 * @returns {string|null} 输出文件OSS地址
 */
async function queryMpsJobResult(jobId, taskType = 'audio') {
  try {
    console.log(`查询MPS任务结果，JobId：${jobId}，任务类型：${taskType}`);

    // 获取阿里云MPS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-mps",
      defaultConfig: {
        regionId: "cn-shanghai",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const regionId = aliyunConfig.config("regionId");
    const outputBucket = aliyunConfig.config("outputBucket");

    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云MPS配置错误，请配置访问密钥");
    }

    // 创建MPS客户端
    const mpsClient = new Core({
      accessKeyId,
      accessKeySecret,
      endpoint: `https://mts.${regionId}.aliyuncs.com`,
      apiVersion: "2014-06-18",
    });

    // 查询任务详情
    const queryParams = {
      JobIds: jobId,
    };

    console.log("调用MPS QueryJobList API，参数：", queryParams);

    const response = await mpsClient.request("QueryJobList", queryParams, {
      method: "POST",
    });

    console.log("MPS QueryJobList API 响应：", response);

    if (response.JobList && response.JobList.Job && response.JobList.Job.length > 0) {
      const job = response.JobList.Job[0];

      if (job.State === "TranscodeSuccess" && job.Output && job.Output.OutputFile) {
        const outputFile = job.Output.OutputFile;
        let outputFileUrl;
        
        // 根据任务类型构建正确的文件URL
        if (taskType === 'audio') {
          // 音频任务：使用MPS返回的实际路径
          outputFileUrl = `https://${outputBucket}.oss-${regionId}.aliyuncs.com/${outputFile.Object}`;
          console.log("获取到音频文件地址：", outputFileUrl);
        } else if (taskType === 'video') {
          // 视频任务：确保使用.mp4格式的输出文件
          // 从UserData中提取taskId来构建正确的视频文件路径
          let taskId = null;
          if (job.UserData) {
            try {
              const userData = JSON.parse(job.UserData);
              taskId = userData.taskId;
            } catch (e) {
              console.warn("解析UserData失败，尝试从输出文件路径提取taskId：", e);
            }
          }
          
          if (taskId) {
            // 使用taskId构建预期的视频文件路径
            outputFileUrl = `https://${outputBucket}.oss-${regionId}.aliyuncs.com/final/${taskId}.mp4`;
          } else {
            // 回退到使用MPS返回的路径，但确保是.mp4格式
            const objectPath = outputFile.Object;
            if (objectPath.endsWith('.mp3')) {
              // 如果路径错误地以.mp3结尾，替换为.mp4
              outputFileUrl = `https://${outputBucket}.oss-${regionId}.aliyuncs.com/${objectPath.replace(/\.mp3$/, '.mp4')}`;
              console.warn("检测到视频文件路径错误，已修正：", outputFileUrl);
            } else {
              outputFileUrl = `https://${outputBucket}.oss-${regionId}.aliyuncs.com/${objectPath}`;
            }
          }
          console.log("获取到最终视频文件地址：", outputFileUrl);
        } else {
          // 其他类型，使用默认逻辑
          outputFileUrl = `https://${outputBucket}.oss-${regionId}.aliyuncs.com/${outputFile.Object}`;
        }
        
        return outputFileUrl;
      } else {
        console.error("MPS任务未成功或输出文件信息不完整：", job);
        return null;
      }
    } else {
      console.error("MPS任务查询结果为空");
      return null;
    }
  } catch (error) {
    console.error("查询MPS任务结果失败：", error);
    return null;
  }
}
