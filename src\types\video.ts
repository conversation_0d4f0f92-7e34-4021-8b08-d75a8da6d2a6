// 视频翻译相关类型定义

// 任务状态枚举
export type TaskStatus =
  | 'uploading'
  | 'extracting_audio'
  | 'recognizing'
  | 'translating'
  | 'merging'
  | 'completed'
  | 'failed'
  | 'cancelled'

// 任务数据结构
export interface VideoTask {
  _id: string
  ossUrl: string
  status: TaskStatus
  audioOssUrl?: string
  subtitleOssUrl?: string
  finalVideoUrl?: string
  errorMessage?: string
  mpsJobId?: string
  paraformerTaskId?: string

  detectedLanguage?: string
  targetLanguage?: string
  userId?: string
  createTime: Date
  updateTime: Date
}

// 选择的视频信息
export interface SelectedVideo {
  tempFilePath: string
  duration: number
  size: number
  name: string
}

// OSS上传策略响应
export interface OSSUploadPolicyResponse {
  code: number
  message: string
  data?: {
    taskId: string
    accessKeyId: string
    host: string
    policy: string
    signature: string
    key: string
    fullUrl: string
  }
  error?: string
}

// 云函数通用响应格式
export interface CloudFunctionResponse<T = any> {
  code: number
  message: string
  data?: T
  error?: string
}
