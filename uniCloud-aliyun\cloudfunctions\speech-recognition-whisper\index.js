// uniCloud云函数：基于OpenAI Whisper的语音识别
"use strict";

const createConfig = require("uni-config-center");

/**
 * 使用OpenAI Whisper进行语音识别
 * 参考simple_subtitle_demo.py的实现
 * 
 * @param {Object} event
 * @param {string} event.taskId - 任务ID
 * @param {string} event.audioOssUrl - 音频文件OSS地址
 * @param {string} event.sourceLanguage - 源语言代码，默认"auto"
 * @returns {Object} 识别结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, audioOssUrl, sourceLanguage = "auto" } = event;

    console.log("speech-recognition-whisper 云函数被调用，参数：", { 
      taskId, 
      audioOssUrl, 
      sourceLanguage 
    });

    // 参数验证
    if (!taskId) {
      return {
        code: 400,
        message: "缺少必要参数：taskId",
      };
    }

    if (!audioOssUrl) {
      return {
        code: 400,
        message: "缺少必要参数：audioOssUrl",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 验证任务是否存在且状态正确
    const taskInfo = await tasksCollection.doc(taskId).get();
    if (!taskInfo.data || taskInfo.data.length === 0) {
      // 如果是测试任务ID，创建一个临时任务用于调试
      if (taskId.startsWith("test-")) {
        console.log("检测到测试任务ID，跳过任务验证");
      } else {
        throw new Error("任务不存在");
      }
    } else {
      if (taskInfo.data[0].status !== "recognizing") {
        console.warn("任务状态不正确，当前状态：", taskInfo.data[0].status);
      }
    }

    // 获取OpenAI API配置
    const openaiConfig = createConfig({
      pluginId: "openai-api",
      defaultConfig: {
        baseUrl: "https://aihubmix.com",
        model: "whisper-large-v3",
      },
    });

    const apiKey = openaiConfig.config("apiKey");
    const baseUrl = openaiConfig.config("baseUrl");
    const model = openaiConfig.config("model");

    // 验证必需的配置
    if (!apiKey) {
      throw new Error("OpenAI API配置缺失，请检查apiKey");
    }

    console.log("Whisper配置检查通过，baseUrl：", baseUrl, "，model：", model);

    try {
      // 调用Whisper进行语音识别
      const recognitionResult = await performWhisperRecognition(
        apiKey,
        baseUrl,
        model,
        audioOssUrl,
        sourceLanguage
      );

      if (!recognitionResult || !recognitionResult.srtContent || recognitionResult.srtContent.trim() === "") {
        console.error("Whisper识别结果详情：", recognitionResult);
        throw new Error("Whisper语音识别返回空结果");
      }

      console.log("Whisper识别完成，SRT长度：", recognitionResult.srtContent.length);

      // 上传SRT文件到OSS
      const uploadResult = await uploadSrtToOSS(taskId, recognitionResult.srtContent);

      // 更新任务状态为translating，准备翻译
      await tasksCollection.doc(taskId).update({
        status: "translating",
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        recognizedLanguage: recognitionResult.detectedLanguage || sourceLanguage,
        updateTime: new Date(),
      });

      return {
        code: 200,
        message: "语音识别成功",
        data: {
          taskId: taskId,
          status: "completed",
          srtLength: recognitionResult.srtContent.length,
          detectedLanguage: recognitionResult.detectedLanguage,
          subtitleOssUrl: uploadResult.subtitleOssUrl,
        },
      };

    } catch (error) {
      console.error("Whisper识别失败：", error);

      // 更新任务状态为失败
      await tasksCollection.doc(taskId).update({
        status: "failed",
        errorMessage: "语音识别失败：" + error.message,
        updateTime: new Date(),
      });

      throw error;
    }

  } catch (error) {
    console.error("speech-recognition-whisper 云函数执行错误：", error);

    return {
      code: 500,
      message: "语音识别失败: " + error.message,
    };
  }
};

/**
 * 执行Whisper语音识别
 * 参考audio-subtitle-serverless.js的FormData实现
 * @param {string} apiKey - API密钥
 * @param {string} baseUrl - API基础URL
 * @param {string} model - 模型名称
 * @param {string} audioOssUrl - 音频文件URL
 * @param {string} sourceLanguage - 源语言代码
 * @returns {Promise<Object>} 识别结果
 */
async function performWhisperRecognition(apiKey, baseUrl, model, audioOssUrl, sourceLanguage) {
  console.log("开始Whisper语音识别，音频URL：", audioOssUrl);

  // 从OSS下载音频文件
  const audioBuffer = await downloadAudioFromOSS(audioOssUrl);
  if (!audioBuffer) {
    throw new Error("无法下载音频文件");
  }

  console.log("音频文件下载完成，大小：", audioBuffer.length, "字节");

  const url = `${baseUrl}/v1/audio/transcriptions`;
  
  try {
    // 尝试使用内置httpclient发送multipart请求
    const response = await sendWhisperRequest(url, audioBuffer, apiKey, model, sourceLanguage);
    
    console.log("Whisper API响应状态码：", response.status);
    
    if (response.status !== 200) {
      console.error("Whisper API请求失败，状态码：", response.status);
      
      // 尝试解析错误响应
      let errorMessage = "未知错误";
      try {
        const errorData = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;
        if (errorData && errorData.error) {
          errorMessage = errorData.error.message || errorData.error;
        }
        console.error("API错误详情：", errorData);
      } catch (e) {
        console.error("原始响应内容：", response.data);
      }
      
      throw new Error(`Whisper API请求失败，状态码: ${response.status}, 错误: ${errorMessage}`);
    }

    let result;
    try {
      // 处理响应数据
      if (typeof response.data === 'string') {
        result = JSON.parse(response.data);
      } else {
        result = response.data;
      }
    } catch (parseError) {
      console.error("解析Whisper响应失败：", parseError);
      console.error("原始响应数据类型：", typeof response.data);
      console.error("原始响应数据：", response.data);
      throw new Error("解析Whisper响应失败: " + parseError.message);
    }
    
    console.log("Whisper API响应完成");
    console.log("响应数据结构：", {
      hasLanguage: !!result.language,
      hasText: !!result.text,
      textLength: result.text ? result.text.length : 0,
      hasSegments: !!result.segments,
      segmentsLength: result.segments ? result.segments.length : 0,
      hasDuration: !!result.duration,
      duration: result.duration
    });
    
    // 输出完整响应数据用于调试
    console.log("完整API响应：", JSON.stringify(result, null, 2));
    
    if (result.language) {
      console.log("检测语言：", result.language);
    }
    
    if (result.text) {
      console.log("识别文本预览：", result.text.substring(0, 200) + (result.text.length > 200 ? "..." : ""));
    }

    // 检查是否有segments数据
    if (!result.segments || !Array.isArray(result.segments) || result.segments.length === 0) {
      console.warn("Whisper响应中没有segments数据，使用整体文本");
      
      // 检查是否有文本内容
      if (!result.text || result.text.trim() === "") {
        console.error("Whisper响应中既没有segments也没有text内容");
        console.error("这可能是API配置或音频文件的问题");
        throw new Error("Whisper识别结果为空，请检查API配置和音频文件");
      }
      
      // 如果没有segments，创建一个包含整个文本的segment
      const duration = result.duration || 60;
      const segments = [{
        start: 0,
        end: duration,
        text: result.text.trim()
      }];
      
      const srtContent = convertSegmentsToSRT(segments);
      console.log("使用整体文本创建SRT，长度：", srtContent.length);
      
      return {
        srtContent: srtContent,
        detectedLanguage: result.language,
      };
    }

    // 转换segments为SRT格式
    console.log(`转换 ${result.segments.length} 个segments为SRT格式`);
    const srtContent = convertSegmentsToSRT(result.segments);

    return {
      srtContent: srtContent,
      detectedLanguage: result.language,
    };
    
  } catch (error) {
    console.error("Whisper API调用失败：", error);
    throw error;
  }
}

/**
 * 发送Whisper API请求
 * 参考audio-subtitle-serverless.js的实现方式
 * @param {string} url - API URL
 * @param {Buffer} audioBuffer - 音频数据
 * @param {string} apiKey - API密钥
 * @param {string} model - 模型名称
 * @param {string} sourceLanguage - 源语言
 * @returns {Promise<Object>} API响应
 */
async function sendWhisperRequest(url, audioBuffer, apiKey, model, sourceLanguage) {
  // 构建标准的multipart/form-data - 参考audio-subtitle-serverless.js的实现
  const boundary = 'WebKitFormBoundary' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  const CRLF = '\r\n';
  
  // 构建form-data各部分 - 严格按照HTTP multipart标准
  const formParts = [];
  
  // file字段
  formParts.push(`--${boundary}${CRLF}`);
  formParts.push(`Content-Disposition: form-data; name="file"; filename="audio.mp3"${CRLF}`);
  formParts.push(`Content-Type: audio/mpeg${CRLF}${CRLF}`);
  
  // model字段
  const modelPart = `${CRLF}--${boundary}${CRLF}Content-Disposition: form-data; name="model"${CRLF}${CRLF}${model}`;
  
  // response_format字段
  const formatPart = `${CRLF}--${boundary}${CRLF}Content-Disposition: form-data; name="response_format"${CRLF}${CRLF}verbose_json`;
  
  // temperature字段
  const tempPart = `${CRLF}--${boundary}${CRLF}Content-Disposition: form-data; name="temperature"${CRLF}${CRLF}0.0`;
  
  // language字段（如果需要）
  let langPart = '';
  if (sourceLanguage && sourceLanguage !== "auto") {
    langPart = `${CRLF}--${boundary}${CRLF}Content-Disposition: form-data; name="language"${CRLF}${CRLF}${sourceLanguage}`;
  }
  
  // 结束边界
  const endBoundary = `${CRLF}--${boundary}--${CRLF}`;
  
  // 组装完整的请求体
  const headerPart = formParts.join('');
  const footerPart = modelPart + formatPart + tempPart + langPart + endBoundary;
  
  const requestBody = Buffer.concat([
    Buffer.from(headerPart, 'utf8'),
    audioBuffer,
    Buffer.from(footerPart, 'utf8')
  ]);

  const headers = {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': `multipart/form-data; boundary=${boundary}`,
    'Content-Length': requestBody.length,
    'Accept': 'application/json',
    'User-Agent': 'uniCloud-whisper-client/1.0'
  };

  console.log("发送Whisper识别请求...");
  console.log("请求参数：", {
    url: url,
    model: model,
    response_format: 'verbose_json',
    temperature: 0.0,
    language: sourceLanguage !== "auto" ? sourceLanguage : undefined,
    fileSize: audioBuffer.length,
    boundaryLength: boundary.length,
    totalSize: requestBody.length
  });

  // 发送请求
  return await uniCloud.httpclient.request(url, {
    method: 'POST',
    headers: headers,
    data: requestBody,
    timeout: 300000, // 5分钟超时
  });
}

/**
 * 从OSS下载音频文件
 * @param {string} ossUrl - OSS文件地址  
 * @returns {Promise<Buffer>} 音频文件Buffer
 */
async function downloadAudioFromOSS(ossUrl) {
  try {
    console.log("从OSS下载音频文件：", ossUrl);
    
    const response = await uniCloud.httpclient.request(ossUrl, {
      method: 'GET',
      timeout: 60000, // 1分钟超时
      dataType: 'stream'
    });

    if (response.status !== 200) {
      throw new Error(`下载音频文件失败，状态码: ${response.status}`);
    }

    return response.data;
  } catch (error) {
    console.error("下载音频文件失败：", error);
    throw new Error(`下载音频文件失败: ${error.message}`);
  }
}

/**
 * 将segments转换为SRT格式
 * 参考simple_subtitle_demo.py的实现
 * @param {Array} segments - Whisper返回的segments数组
 * @returns {string} SRT格式字符串
 */
function convertSegmentsToSRT(segments) {
  let srtContent = "";
  
  segments.forEach((segment, index) => {
    const startTime = formatSRTTime(segment.start);
    const endTime = formatSRTTime(segment.end);
    const text = segment.text.trim();

    if (text) {
      srtContent += `${index + 1}\n`;
      srtContent += `${startTime} --> ${endTime}\n`;
      srtContent += `${text}\n\n`;
    }
  });

  return srtContent.trim();
}

/**
 * 格式化SRT时间格式
 * 参考simple_subtitle_demo.py的实现
 * @param {number} seconds - 秒数
 * @returns {string} SRT时间格式 (HH:MM:SS,mmm)
 */
function formatSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * 上传SRT文件到OSS
 * @param {string} taskId - 任务ID
 * @param {string} srtContent - SRT内容
 * @returns {Promise<Object>} 上传结果
 */
async function uploadSrtToOSS(taskId, srtContent) {
  const OSS = require("ali-oss");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成SRT文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_whisper_${timestamp}.srt`;

    // 上传SRT内容到OSS
    const uploadResult = await client.put(objectKey, Buffer.from(srtContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传SRT文件完成，地址：", subtitleOssUrl);

    return {
      subtitleOssUrl: subtitleOssUrl,
      objectKey: objectKey,
    };

  } catch (error) {
    console.error("上传SRT文件失败：", error);
    throw new Error("上传字幕文件失败：" + error.message);
  }
}