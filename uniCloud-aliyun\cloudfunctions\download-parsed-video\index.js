"use strict";

/**
 * 下载解析到的视频的云函数
 * 根据parseResult中的视频地址进行下载，然后上传到OSS
 */

const createConfig = require("uni-config-center");

exports.main = async (event) => {
  console.log("下载解析视频云函数被调用:", event);

  const { parseResult, taskId, openid, sourceLanguage = "auto", targetLanguage = "zh" } = event;

  // 参数验证
  if (!parseResult) {
    return {
      code: -1,
      message: "缺少必要参数：parseResult",
    };
  }

  if (!parseResult.videoUrl) {
    return {
      code: -1,
      message: "parseResult中缺少有效的视频地址",
    };
  }

  if (!taskId) {
    return {
      code: -1,
      message: "缺少必要参数：taskId",
    };
  }

  if (!openid) {
    return {
      code: -1,
      message: "缺少必要参数：openid",
    };
  }

  try {
    const videoUrl = parseResult.videoUrl;
    console.log("开始下载视频:", videoUrl);

    // 下载视频到临时文件
    const downloadResult = await downloadVideoToTemp(videoUrl, taskId);

    console.log("视频下载完成，开始上传到OSS");

    // 上传到OSS
    const ossResult = await uploadVideoToOSS(
      downloadResult.tempFilePath,
      downloadResult.fileName,
      taskId
    );

    console.log("视频上传到OSS完成:", ossResult.url);

    // 构建视频信息，包含parseResult的所有信息
    const videoInfo = {
      fileName: downloadResult.fileName,
      fileSize: downloadResult.fileSize,
      duration: downloadResult.duration,
      platform: parseResult.platform || "未知平台",
      author: parseResult.author?.name || "未知作者",
      title: parseResult.title || "未知标题",
      cover: parseResult.video?.cover || "",
      originalUrl: videoUrl,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
    };

    // 更新数据库任务状态
    const db = uniCloud.database();
    await db.collection("tasks").doc(taskId).update({
      status: "extracting_audio",
      ossUrl: ossResult.url,
      videoInfo: videoInfo,
      updateTime: new Date(),
    });

    // 启动音频提取任务（异步，不等待结果）
    uniCloud
      .callFunction({
        name: "process-video-task",
        data: {
          taskId: taskId,
          ossUrl: ossResult.url,
          action: "extract_audio",
        },
      })
      .then((processResult) => {
        console.log("调用音频提取任务结果:", processResult);
      })
      .catch((error) => {
        console.error("启动处理任务失败:", error);
      });

    return {
      code: 0,
      message: "视频下载并上传成功",
      data: {
        taskId: taskId,
        ossUrl: ossResult.url,
        fileInfo: {
          fileName: downloadResult.fileName,
          fileSize: downloadResult.fileSize,
          duration: downloadResult.duration,
        },
      },
    };
  } catch (error) {
    console.error("下载解析视频失败:", error);

    // 更新任务状态为失败
    try {
      const db = uniCloud.database();
      await db
        .collection("tasks")
        .doc(taskId)
        .update({
          status: "failed",
          errorMessage: "视频下载失败: " + error.message,
          updateTime: new Date(),
        });
    } catch (updateError) {
      console.error("更新任务失败状态时出错:", updateError);
    }

    return {
      code: -1,
      message: "视频下载失败: " + error.message,
    };
  }
};

/**
 * 下载视频到临时文件
 * @param {string} videoUrl - 视频URL
 * @param {string} taskId - 任务ID，用于生成文件名
 * @param {number} maxRedirects - 最大重定向次数，默认为5
 * @returns {Promise<Object>} 下载结果
 */
async function downloadVideoToTemp(videoUrl, taskId, maxRedirects = 5) {
  const https = require("https");
  const http = require("http");
  const url = require("url");
  const path = require("path");
  const fs = require("fs");
  const os = require("os");

  return new Promise((resolve, reject) => {
    try {
      // 检查重定向次数
      if (maxRedirects <= 0) {
        reject(new Error("重定向次数过多，下载失败"));
        return;
      }
      
      const parsedUrl = url.parse(videoUrl);
      const client = parsedUrl.protocol === "https:" ? https : http;

      // 使用任务ID作为文件名
      const fileName = `${taskId}.mp4`;
      const tempFilePath = path.join(os.tmpdir(), fileName);

      console.log("开始下载视频:", videoUrl, "剩余重定向次数:", maxRedirects);
      console.log("下载视频到临时文件:", tempFilePath);

      const req = client.get(
        videoUrl,
        {
          timeout: 30000,
          headers: {
            "User-Agent":
              "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
          },
        },
        (res) => {
          // 处理重定向
          if (res.statusCode === 301 || res.statusCode === 302) {
            let redirectUrl = res.headers.location;
            if (redirectUrl) {
              // 处理相对URL
              if (redirectUrl.startsWith('/')) {
                const originalUrl = url.parse(videoUrl);
                redirectUrl = `${originalUrl.protocol}//${originalUrl.host}${redirectUrl}`;
              } else if (!redirectUrl.startsWith('http')) {
                const originalUrl = url.parse(videoUrl);
                redirectUrl = `${originalUrl.protocol}//${originalUrl.host}/${redirectUrl}`;
              }
              
              console.log(`处理重定向: ${res.statusCode} -> ${redirectUrl}`);
              // 递归调用处理重定向，减少重定向次数
              downloadVideoToTemp(redirectUrl, taskId, maxRedirects - 1)
                .then(resolve)
                .catch(reject);
              return;
            } else {
              reject(new Error(`重定向失败，状态码: ${res.statusCode}，缺少location头`));
              return;
            }
          }
          
          if (res.statusCode !== 200) {
            reject(new Error(`下载失败，状态码: ${res.statusCode}`));
            return;
          }

          const fileStream = fs.createWriteStream(tempFilePath);
          let downloadedBytes = 0;
          const totalBytes = parseInt(res.headers["content-length"]) || 0;

          res.on("data", (chunk) => {
            downloadedBytes += chunk.length;
            if (totalBytes > 0) {
              const progress = Math.round((downloadedBytes / totalBytes) * 100);
              console.log(`下载进度: ${progress}%`);
            }
          });

          res.pipe(fileStream);

          fileStream.on("finish", () => {
            fileStream.close();

            // 获取文件信息
            fs.stat(tempFilePath, (err, stats) => {
              if (err) {
                console.error("获取文件信息失败:", err);
                resolve({
                  tempFilePath,
                  fileName,
                  fileSize: downloadedBytes,
                  duration: 0, // 让服务器端在处理时获取真实时长
                });
              } else {
                resolve({
                  tempFilePath,
                  fileName,
                  fileSize: stats.size,
                  duration: 0, // 让服务器端在处理时获取真实时长
                });
              }
            });
          });

          fileStream.on("error", (error) => {
            console.error("文件写入失败:", error);
            // 清理临时文件
            if (fs.existsSync(tempFilePath)) {
              fs.unlinkSync(tempFilePath);
            }
            reject(new Error("文件写入失败"));
          });
        }
      );

      req.on("error", (error) => {
        console.error("视频下载请求失败:", error);
        reject(new Error("视频下载请求失败: " + error.message));
      });

      req.on("timeout", () => {
        req.destroy();
        reject(new Error("下载超时"));
      });
    } catch (error) {
      console.error("下载视频过程出错:", error);
      reject(error);
    }
  });
}

/**
 * 上传视频文件到OSS
 * @param {string} tempFilePath - 临时文件路径
 * @param {string} fileName - 文件名
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 上传结果
 */
async function uploadVideoToOSS(tempFilePath, fileName, taskId) {
  const OSS = require("ali-oss");
  const fs = require("fs");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
      secure: true // 强制使用HTTPS
    });

    // 生成OSS对象键，保持与项目命名规范一致
    const timestamp = Date.now();
    const objectKey = `video/task_${taskId}_${timestamp}.mp4`;

    console.log("开始上传到OSS:", objectKey);

    // 上传文件到OSS
    const uploadResult = await client.put(objectKey, tempFilePath, {
      headers: {
        "Content-Type": "video/mp4",
      },
    });

    console.log("OSS上传完成，URL:", uploadResult.url);

    return {
      url: uploadResult.url,
      objectKey: objectKey,
    };
  } finally {
    // 清理临时文件
    if (fs.existsSync(tempFilePath)) {
      try {
        fs.unlinkSync(tempFilePath);
        console.log("临时文件已清理:", tempFilePath);
      } catch (error) {
        console.error("清理临时文件失败:", error);
      }
    }
  }
}
