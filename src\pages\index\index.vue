<template>
  <view class="home-container">
    <!-- 现代化头部区域 -->
    <view class="hero-section">
      <view class="hero-content">
        <view class="hero-text">
          <text class="hero-title">智能字幕胶囊</text>
          <text class="hero-subtitle">AI驱动的视频字幕生成工具</text>
          <text class="hero-description">让每个视频都拥有精准的字幕，提升观看体验</text>
        </view>
      </view>

      <!-- 快速开始按钮 -->
      <view class="hero-action">
        <button @click="startUpload" class="btn btn-primary btn-lg start-btn">
          <text class="btn-icon">🚀</text>
          <text>开始制作</text>
        </button>
        <text class="hero-tip">支持MP4、MOV等格式，最大200MB</text>
      </view>
    </view>

    <!-- 功能特性卡片 -->
    <view class="features-section">
      <view class="section-header">
        <text class="section-title">核心功能</text>
        <text class="section-subtitle">AI智能处理，简单高效</text>
      </view>

      <view class="features-grid">
        <!-- 视频上传卡片 - 包含格式信息 -->
        <view class="feature-card">
          <view class="feature-icon-wrapper">
            <text class="feature-icon">🎬</text>
          </view>
          <view class="feature-content">
            <text class="feature-title">视频上传</text>
            <text class="feature-desc"
              >支持支持 {{ supportedFormats.map((f) => f.extension).join("、") }} 格式。最大{{
                uploadConfig.maxFileSizeMB
              }}MB</text
            >
          </view>
        </view>

        <!-- 语音识别卡片 -->
        <view class="feature-card">
          <view class="feature-icon-wrapper">
            <text class="feature-icon">🎤</text>
          </view>
          <view class="feature-content">
            <text class="feature-title">语音识别</text>
            <text class="feature-desc"
              >AI智能识别语音内容，支持
              {{
                supportedLanguages
                  .slice(0, 4)
                  .map((l) => l.displayName)
                  .join("、")
              }}
              等{{ supportedLanguages.length }}种语言</text
            >
          </view>
        </view>

        <!-- 字幕生成卡片 - 包含语言信息 -->
        <view class="feature-card">
          <view class="feature-icon-wrapper">
            <text class="feature-icon">📝</text>
          </view>
          <view class="feature-content">
            <text class="feature-title">字幕生成</text>
            <text class="feature-desc">AI智能翻译，生成精准字幕，支持多语言实时转换</text>
          </view>
        </view>

        <!-- 精准同步卡片 -->
        <view class="feature-card">
          <view class="feature-icon-wrapper">
            <text class="feature-icon">🎯</text>
          </view>
          <view class="feature-content">
            <text class="feature-title">精准同步</text>
            <text class="feature-desc">Paraformer-v2模型识别，字幕与视频画面完美同步</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近处理 -->
    <view v-if="recentTasks.length > 0" class="recent-section">
      <view class="section-header">
        <text class="section-title">最近处理</text>
        <text class="more-link" @click="viewAllHistory">查看全部 →</text>
      </view>

      <view class="recent-cards">
        <view
          v-for="task in recentTasks"
          :key="task._id"
          class="recent-card card"
          @click="viewTaskDetail(task)"
        >
          <view class="recent-card-content">
            <view class="task-info">
              <text class="task-name text-ellipsis">{{ task.fileName || "未命名视频" }}</text>
              <text class="task-time">{{ formatTime(task.createTime) }}</text>
            </view>
            <view class="task-status-wrapper">
              <view class="tag" :class="getStatusTagClass(task.status)">
                <text>{{ getStatusText(task.status) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用指南 -->
    <view class="guide-section">
      <view class="section-header">
        <text class="section-title">使用指南</text>
        <text class="section-subtitle">三步完成视频字幕制作</text>
      </view>

      <view class="guide-steps">
        <view class="guide-step" v-for="(step, index) in guideSteps" :key="index">
          <view class="step-indicator">
            <text class="step-number">{{ index + 1 }}</text>
          </view>
          <view class="step-content">
            <text class="step-title">{{ step.title }}</text>
            <text class="step-desc">{{ step.desc }}</text>
          </view>
          <view v-if="index < guideSteps.length - 1" class="step-connector"></view>
        </view>
      </view>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getVideoUploadConfig } from "@/config";

// 响应式数据
const recentTasks = ref<any[]>([]);

// 获取视频上传配置
const uploadConfig = getVideoUploadConfig();

// 支持的翻译语言数据
const supportedLanguages = ref([
  {
    code: "en",
    name: "English",
    displayName: "英文",
    icon: "🇺🇸",
  },
  {
    code: "ja",
    name: "Japanese",
    displayName: "日语",
    icon: "🇯🇵",
  },
  {
    code: "ko",
    name: "Korean",
    displayName: "韩语",
    icon: "🇰🇷",
  },
  {
    code: "de",
    name: "German",
    displayName: "德语",
    icon: "🇩🇪",
  },
  {
    code: "fr",
    name: "French",
    displayName: "法语",
    icon: "🇫🇷",
  },
  {
    code: "ru",
    name: "Russian",
    displayName: "俄语",
    icon: "🇷🇺",
  },
]);

// 支持的视频格式数据
const supportedFormats = ref([
  {
    extension: "MP4",
    description: "最常用的视频格式",
    icon: "🎬",
  },
  {
    extension: "MOV",
    description: "Apple设备常用格式",
    icon: "📱",
  },
  {
    extension: "AVI",
    description: "经典视频格式",
    icon: "💿",
  },
  {
    extension: "WMV",
    description: "Windows媒体格式",
    icon: "🖥️",
  },
]);

// 使用指南数据
const guideSteps = ref([
  {
    title: "选择视频",
    desc: "上传您的视频文件，支持多种格式",
  },
  {
    title: "AI处理",
    desc: "智能识别语音并生成字幕",
  },
  {
    title: "下载结果",
    desc: "预览并下载带字幕的视频",
  },
]);

// 页面加载时获取最近任务
onMounted(() => {
  loadRecentTasks();
});

// 加载最近任务
const loadRecentTasks = async () => {
  // 实现省略...
};

// 开始上传
const startUpload = () => {
  uni.switchTab({
    url: "/pages/upload/upload",
  });
};

// 查看所有历史
const viewAllHistory = () => {
  uni.switchTab({
    url: "/pages/history/history",
  });
};

// 查看任务详情
const viewTaskDetail = (task: any) => {
  if (task.status === "completed") {
    uni.navigateTo({
      url: `/pages/result/result?taskId=${task.id}`,
    });
  } else if (task.status === "processing") {
    uni.navigateTo({
      url: `/pages/process/process?taskId=${task.id}`,
    });
  } else {
    uni.showToast({
      title: "任务处理失败",
      icon: "none",
    });
  }
};

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case "processing":
      return "处理中";
    case "completed":
      return "已完成";
    case "failed":
      return "失败";
    default:
      return "未知";
  }
};

// 获取状态标签样式类
const getStatusTagClass = (status: string): string => {
  switch (status) {
    case "processing":
      return "tag-primary";
    case "completed":
      return "tag-success";
    case "failed":
      return "tag-error";
    default:
      return "tag-primary";
  }
};

// 格式化时间
const formatTime = (timestamp: string): string => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  if (diff < 60000) return "刚刚";
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;

  return `${date.getMonth() + 1}-${date.getDate()}`;
};
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding-bottom: 120rpx;
}

/* 精美的头部区域 */
.hero-section {
  padding: 60rpx 32rpx 80rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  position: relative;
  overflow: hidden;
  margin-bottom: 60rpx;
  text-align: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.hero-section::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -10%;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
  border-radius: 50%;
}

.hero-content {
  position: relative;
  z-index: 2;
  margin-bottom: 60rpx;
}

.hero-text {
  max-width: 600rpx;
  margin: 0 auto;
}

.hero-title {
  display: block;
  font-size: 60rpx;
  font-weight: 800;
  color: white;
  margin-bottom: 24rpx;
  line-height: 1.1;
  letter-spacing: -1rpx;
  position: relative;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 4rpx;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.2) 100%);
  border-radius: 2rpx;
}

.hero-subtitle {
  display: block;
  font-size: 34rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 20rpx;
}

.hero-description {
  display: block;
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.6;
  max-width: 520rpx;
  margin: 0 auto 40rpx;
}

.hero-action {
  text-align: center;
  position: relative;
  z-index: 2;
}

.start-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  font-size: 34rpx;
  font-weight: 600;
  padding: 24rpx 48rpx;
  border-radius: 30rpx;
  box-shadow: 
    0 8rpx 25rpx rgba(255, 107, 107, 0.35),
    0 0 0 1rpx rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
}

.start-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.start-btn:active {
  transform: translateY(-3rpx);
  box-shadow: 
    0 12rpx 35rpx rgba(255, 107, 107, 0.4),
    0 0 0 1rpx rgba(255, 255, 255, 0.15);
}

.start-btn:active::before {
  transform: translateX(100%);
}

.btn-icon {
  margin-right: 16rpx;
  font-size: 34rpx;
}

.hero-tip {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.75);
  margin-top: 20rpx;
  font-weight: 500;
}

/* 功能特性区域 */
.features-section {
  padding: 0 32rpx 80rpx;
}

.section-header {
  text-align: center;
  margin-bottom: 64rpx;
}

.section-title {
  display: block;
  font-size: 52rpx;
  font-weight: 800;
  color: #1f2937;
  margin-bottom: 20rpx;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 2rpx;
}

.section-subtitle {
  display: block;
  font-size: 28rpx;
  color: #525252;
  line-height: 1.5;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
  max-width: 800rpx;
  margin: 0 auto;
}

.feature-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 
    0 4rpx 20rpx rgba(0, 0, 0, 0.06),
    0 1rpx 3rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.feature-card:active {
  transform: translateY(-8rpx);
  box-shadow: 
    0 12rpx 40rpx rgba(99, 102, 241, 0.15),
    0 4rpx 12rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(99, 102, 241, 0.2);
}

.feature-card:active::before {
  transform: scaleX(1);
}

.feature-icon-wrapper {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 28rpx;
  box-shadow: 
    0 8rpx 25rpx rgba(99, 102, 241, 0.25),
    0 0 0 1rpx rgba(255, 255, 255, 0.1);
  transition: all 0.4s ease;
  position: relative;
}

.feature-icon-wrapper::after {
  content: '';
  position: absolute;
  inset: 3rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: 19rpx;
  pointer-events: none;
}

.feature-card:active .feature-icon-wrapper {
  transform: scale(1.05);
  box-shadow: 
    0 12rpx 35rpx rgba(99, 102, 241, 0.35),
    0 0 0 1rpx rgba(255, 255, 255, 0.15);
}

.feature-icon {
  font-size: 44rpx;
  color: white;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.feature-content {
  text-align: center;
}

.feature-title {
  display: block;
  font-size: 34rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.feature-desc {
  display: block;
  font-size: 28rpx;
  color: #64748b;
  line-height: 1.6;
  font-weight: 500;
}

/* 功能卡片详细信息 */
.feature-details {
  margin-top: 16rpx;
}

.detail-text {
  display: block;
  font-size: 24rpx;
  color: #4b5563;
  line-height: 1.5;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.detail-note {
  font-size: 20rpx;
  color: #6b7280;
  text-align: center;
  font-weight: 500;
}

/* 最近处理区域 */
.recent-section {
  padding: 0 32rpx 80rpx;
}

.recent-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 48rpx;
}

.recent-section .section-title {
  font-size: 44rpx;
  font-weight: 800;
  color: #1f2937;
}

.more-link {
  font-size: 30rpx;
  color: #6366f1;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.3s ease;
}

.more-link:active {
  color: #4f46e5;
}

.recent-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20rpx;
  padding: 36rpx;
  box-shadow: 
    0 4rpx 20rpx rgba(0, 0, 0, 0.06),
    0 1rpx 3rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.recent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.recent-card:active {
  transform: translateY(-4rpx);
  box-shadow: 
    0 8rpx 30rpx rgba(99, 102, 241, 0.12),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(99, 102, 241, 0.2);
}

.recent-card:active::before {
  transform: scaleX(1);
}

.recent-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  flex: 1;
  margin-right: 24rpx;
}

.task-name {
  display: block;
  font-size: 34rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
  line-height: 1.2;
}

.task-time {
  display: block;
  font-size: 26rpx;
  color: #94a3b8;
  font-weight: 500;
}

.task-status-wrapper {
  flex-shrink: 0;
}

/* 使用指南区域 */
.guide-section {
  padding: 0 32rpx 80rpx;
}

.guide-section .section-header {
  text-align: center;
  margin-bottom: 64rpx;
}

.guide-steps {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 28rpx;
  padding: 56rpx 40rpx;
  box-shadow: 
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 1rpx 3rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  max-width: 700rpx;
  margin: 0 auto;
  position: relative;
}

.guide-steps::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 28rpx 28rpx 0 0;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  position: relative;
  margin-bottom: 56rpx;
}

.guide-step:last-child {
  margin-bottom: 0;
}

.step-indicator {
  flex-shrink: 0;
  margin-right: 32rpx;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: 800;
  box-shadow: 
    0 8rpx 25rpx rgba(99, 102, 241, 0.3),
    0 0 0 1rpx rgba(255, 255, 255, 0.1);
  position: relative;
}

.step-number::after {
  content: '';
  position: absolute;
  inset: 3rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: 50%;
  pointer-events: none;
}

.step-content {
  flex: 1;
  padding-top: 12rpx;
}

.step-title {
  display: block;
  font-size: 34rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.step-desc {
  display: block;
  font-size: 30rpx;
  color: #64748b;
  line-height: 1.6;
  font-weight: 500;
}

.step-connector {
  position: absolute;
  left: 44rpx;
  top: 88rpx;
  width: 6rpx;
  height: 56rpx;
  background: linear-gradient(180deg, #6366f1 0%, #e2e8f0 100%);
  border-radius: 3rpx;
  z-index: 1;
  box-shadow: 0 0 8rpx rgba(99, 102, 241, 0.2);
}

/* 底部空间 */
.bottom-space {
  height: 48rpx;
}

/* 精致的动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.feature-card {
  animation: slideInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }

.guide-steps {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

.recent-card {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

.recent-card:nth-child(1) { animation-delay: 0.1s; }
.recent-card:nth-child(2) { animation-delay: 0.2s; }
.recent-card:nth-child(3) { animation-delay: 0.3s; }

/* 精美的状态标签 */
.tag {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.tag-primary {
  background: linear-gradient(135deg, #1890ff 0%, #0969da 100%);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(24, 144, 255, 0.3);
}

.tag-success {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.3);
}

.tag-error {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(255, 77, 79, 0.3);
}

/* 响应式设计优化 */
@media screen and (max-width: 750rpx) {
  .hero-section {
    padding: 50rpx 24rpx 70rpx;
  }
  
  .hero-title {
    font-size: 52rpx;
  }
  
  .hero-subtitle {
    font-size: 30rpx;
  }
  
  .hero-description {
    font-size: 28rpx;
  }
  
  .start-btn {
    font-size: 32rpx;
    padding: 22rpx 42rpx;
  }
  
  .features-section {
    padding: 0 24rpx 60rpx;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 24rpx;
    max-width: 500rpx;
  }
  
  .feature-card {
    padding: 36rpx 28rpx;
  }
  
  .feature-icon-wrapper {
    width: 80rpx;
    height: 80rpx;
  }
  
  .feature-icon {
    font-size: 40rpx;
  }
  
  .section-title {
    font-size: 44rpx;
  }
  
  .guide-section {
    padding: 0 24rpx 60rpx;
  }
  
  .guide-steps {
    padding: 48rpx 32rpx;
  }
  
  .step-number {
    width: 80rpx;
    height: 80rpx;
    font-size: 32rpx;
  }
  
  .step-connector {
    left: 40rpx;
    width: 4rpx;
  }
  
  .recent-section {
    padding: 0 24rpx 60rpx;
  }
}
</style>
