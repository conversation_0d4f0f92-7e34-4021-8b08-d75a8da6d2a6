### **技术方案：基于阿里云服务的视频翻译与烧录（OSS 上传优化版）**

#### **一、 整体架构（已优化）**

```mermaid
graph TD
    subgraph A[小程序端]
        A1(uni.chooseVideo) --> B1
        A2(uni.uploadFile) --> C5[OSS]
        A3(uni.downloadFile)
    end

    subgraph B[uniCloud云函数]
        B1(get-upload-policy)
        B2(handle-media-callback)
        B3(process-video-task)
        B4(get-download-url)
        D[云数据库/DB]
    end

    subgraph C[阿里云服务]
        C5 -- OSS Event Trigger --> B2
        C2[Paraformer]
        C3[MT]
        C4[MPS]
    end

    B1 --> A2
    B2 --> B3
    B3 -- 提交任务 --> C2
    B3 -- 提交任务 --> C3
    B3 -- 提交任务 --> C4
    C4 -- MPS Callback --> B2
    B2 -- 更新状态 --> D
    B3 -- 更新状态 --> D
    A3 -- 请求URL --> B4
    B4 -- 生成签名URL --> C5
    C5 -- Signed URL --> A3
```

**核心变化解读**：

1.  **上传目标**：小程序端不再使用 VOD SDK，而是通过 `uni.uploadFile` 直接上传到 **OSS**。
2.  **上传凭证**：云函数 `get-upload-auth` 变更为 `get-upload-policy`，它不再创建 VOD 任务，而是生成 OSS 的临时上传策略（Policy）。
3.  **任务触发**：整个流程的起点不再是 VOD 上传回调，而是 **OSS 的 `ObjectCreated` 事件**。该事件会直接触发云函数 `handle-media-callback`。
4.  **函数复用**：`handle-media-callback` 函数将统一处理来自 OSS 的初始触发和来自 MPS（媒体处理）的后续任务完成回调。

---

#### **二、 数据库任务表（`tasks`）核心字段（已优化）**

- `_id`: String (主键, 作为`taskId`)
- `userId`: String (关联用户)
- `originalVideoOssKey`: String (**新增**: 视频在 OSS 中的完整路径，如 `uploads/user123/video.mp4`，用于关联所有资源)
- `status`: String (`pending_upload`, `processing`, `recognizing`, `translating`, `merging`, `completed`, `failed`)
- `audioOssUrl`: String
- `subtitleOssUrl`: String
- `finalVideoUrl`: String
- `errorMessage`: String
- `createdAt`: Timestamp
- `updatedAt`: Timestamp

---

### **三、 分步执行流程（已优化）**

#### **步骤 1: 获取 OSS 上传策略**

1.  **小程序端**: 调用 `uni.chooseVideo`，获取 `tempFilePath` 和 `fileName`。
2.  **小程序端**: 调用云函数 `get-upload-policy`，传入 `fileName`。
3.  **云函数 `get-upload-policy`**:

    - 生成一个唯一的 `objectKey` (OSS 中的文件路径)，例如 `uploads/${userId}/${Date.now()}-${fileName}`。
    - 在数据库 `tasks` 表中插入一条新记录，状态为 `pending_upload`，并存入 `originalVideoOssKey`。
    - 调用 **OSS SDK** 生成一个带有时效性的**上传策略（Post Policy）**。
    - 返回 `policy`, `signature`, `accessKeyId`, `host` (OSS endpoint) 和 `key` (`objectKey`) 给小程序端。
    - **代码示例 (`get-upload-policy/index.js`)**:

    ```javascript
    // 引入ali-oss
    const OSS = require("ali-oss");
    const client = new OSS({
      /* ...OSS配置... */
    });

    // ... 数据库操作，创建任务记录 ...
    const taskId = dbResult.id;
    const objectKey = `uploads/${event.userId}/${Date.now()}-${event.fileName}`;
    await db.collection("tasks").doc(taskId).update({ originalVideoOssKey: objectKey });

    const policy = client.calculatePostSignature({
      // policy的有效期，单位秒
      expire: 300,
      conditions: [
        // 限制文件大小等
        ["content-length-range", 0, 1024 * 1024 * 100], // 100MB
      ],
    });

    return {
      accessKeyId: client.options.accessKeyId,
      host: client.options.endpoint, // 例如: oss-cn-hangzhou.aliyuncs.com
      policy: policy.policy,
      signature: policy.signature,
      key: objectKey, // 返回给前端，用于上传
      taskId: taskId,
    };
    ```

#### **步骤 2: 上传视频至 OSS**

1.  **小程序端**: 使用 `uni.uploadFile` API，将上一步获取的策略信息和文件一并上传。
    - **代码示例 (小程序端)**:
    ```javascript
    uni.uploadFile({
      url: policyResult.host, // OSS的域名
      filePath: tempFilePath,
      name: "file", // 必须是 'file'
      formData: {
        key: policyResult.key,
        policy: policyResult.policy,
        OSSAccessKeyId: policyResult.accessKeyId,
        signature: policyResult.signature,
        success_action_status: "200", // 可选，成功后返回200状态码
      },
      success: (res) => {
        if (res.statusCode === 200) {
          console.log("上传成功！开始轮询任务状态...");
          // 此处可以开始轮询任务状态
          startPolling(policyResult.taskId);
        }
      },
    });
    ```

#### **步骤 3: 触发处理流程与提取音频**

1.  **触发**: 视频成功上传到 OSS 后，**OSS 事件通知**（需在阿里云 OSS 控制台配置）会触发云函数 `handle-media-callback`。
    - **配置**：在 OSS Bucket -> 数据处理 -> 事件通知中，创建规则，事件类型选择 `oss:ObjectCreated:PostObject`，目标服务选择**函数计算 (uniCloud 本质上也是)**，并指定 `handle-media-callback` 函数。
2.  **云函数 `handle-media-callback`**:
    - **解析事件源**:
      - 如果事件来自 **OSS** (首次触发)，则解析出 `bucket` 和 `key` (`originalVideoOssKey`)。
      - 如果事件来自 **MPS** (后续回调)，则解析出 `JobId`, `Status` 和 `UserData`。
    - **首次触发逻辑**:
      - 通过 `originalVideoOssKey` 从数据库中找到对应的任务。
      - 验证任务状态是否为 `pending_upload`，防止重复处理。
      - 更新数据库任务状态为 `processing`。
      - **调用云函数 `process-video-task`**，传入 `taskId` 和 `originalVideoOssKey`。
3.  **云函数 `process-video-task`**:
    - 调用 **MPS `SubmitJobs` API**，提交一个“提取音频”的作业。
    - **重要**: 在 `SubmitJobs` 的 `UserData` 字段中传入 `{"taskId": "数据库任务ID", "action": "extract_audio"}`，用于回调时识别任务。
    - 输入（Input）为刚上传的视频 OSS 地址，输出（Output）指定一个存放音频的 OSS 路径。

#### **步骤 4: 语音识别与翻译**

1.  **触发**: MPS 音频提取任务完成后，通过**MPS 回调**（需在 MPS 管道配置中设置）再次触发 `handle-media-callback`。
2.  **云函数 `handle-media-callback`**:
    - 解析事件，获取 `taskId`, `action` 和 MPS 任务结果（即音频文件的 OSS 地址 `audioOssUrl`）。
    - 根据 `taskId` 更新数据库，存入 `audioOssUrl`，并将状态更新为 `recognizing`。
    - **再次调用 `process-video-task`**，传入 `taskId` 和 `audioOssUrl`。
3.  **云函数 `process-video-task` (识别与翻译逻辑)**:
    - **识别**: 调用 **Paraformer** `SubmitAudioFileTask` API，传入 `audioOssUrl`，请求 SRT 格式输出。
    - **等待结果**: Paraformer 是异步任务，你需要轮询或使用其回调来获取结果。获取到 SRT 文本和识别出的语言。
    - **翻译 (条件性)**: 如果语言不是目标语言（如`zh`），则更新任务状态为 `translating`。解析 SRT，批量调用 **MT** 服务翻译文本内容，然后重组为新的 SRT 字符串。
    - **上传 SRT**: 将最终的 SRT 内容上传到 OSS，得到 `subtitleOssUrl`，并更新数据库。

#### **步骤 5: 字幕烧录**

1.  **云函数 `process-video-task`**:
    - 获取到 `subtitleOssUrl` 后，更新数据库任务状态为 `merging`。
    - **再次调用 MPS `SubmitJobs` API**，提交一个“字幕烧录”的作业。
    - 在 `UserData` 中传入 `{"taskId": "数据库任务ID", "action": "burn_subtitle"}`。
    - **代码示例 (关键部分)**:
    ```javascript
    // const mps = new MPSClient();
    await mps.submitJobs({
      // ...
      Input: { Bucket: "your-bucket", Location: "oss-cn-hangzhou", Object: originalVideoOssKey }, // 从DB读取
      Output: { Bucket: "your-bucket", Object: `finals/${taskId}.mp4` },
      Transcode: {
        // 定义视频转码参数
        // ...
      },
      SubtitleConfig: {
        SubtitleList: [
          {
            Map: {
              Bucket: "your-bucket",
              Location: "oss-cn-hangzhou",
              Object: subtitleOssUrl.split(".com/")[1], // 从完整的OSS URL中提取Object Key
            },
            // ... 字体样式配置 ...
          },
        ],
      },
    });
    ```

#### **步骤 6: 完成与下载**

1.  **触发**: MPS 烧录任务完成后，再次通过回调触发 `handle-media-callback`。
2.  **云函数 `handle-media-callback`**:
    - 解析事件，确认 `action` 是 `burn_subtitle` 且任务成功。
    - 获取最终视频的 OSS Key，拼接成完整的 `finalVideoUrl`。
    - 更新数据库中对应任务的状态为 **`completed`**，并存入 `finalVideoUrl`。
3.  **小程序端**: 通过定时器轮询任务状态（或使用 WebSocket 接收实时推送）。发现状态为 `completed` 后，调用云函数 `get-download-url`。
4.  **云函数 `get-download-url`**:
    - 根据传入的 `taskId`，从数据库查到 `finalVideoUrl` 对应的 `objectKey`。
    - 使用 OSS SDK 的 `signatureUrl` 方法生成一个带时效性的签名 URL，供下载使用。
5.  **小程序端**: 使用返回的 `signedUrl`，调用 `uni.downloadFile` 和 `uni.saveVideoToPhotosAlbum`。

---

### **四、 核心优势与注意事项**

- **优势**:

  - **成本**：OSS 存储成本远低于 VOD 存储成本，对于仅需处理和分发的场景更经济。
  - **灵活性**：所有媒体资产（原视频、音频、字幕、成品）都在你的 OSS Bucket 中，路径清晰，管理方便，易于与其他云服务集成。
  - **解耦**：上传服务与媒体处理服务完全解耦，架构更清晰。

- **注意事项**:
  - **OSS 事件配置**: 需要正确配置 OSS 的事件通知规则，确保能准确触发云函数。这是新方案的关键连接点。
  - **MPS 回调配置**: 需要在 MPS 的管道中配置好回调通知，以便接收处理结果。
  - **幂等性处理**: `handle-media-callback` 会被多次触发，必须通过 `taskId` 和 `status` 仔细判断当前任务阶段，避免重复执行。
  - **权限管理**: 需要为云函数执行角色（RAM Role）精细授权，确保它有权限读写指定的 OSS 路径，以及调用 MPS、Paraformer、MT 等服务。
  - **错误处理**: 在 `process-video-task` 的每一步都应有 `try...catch`，一旦出错，立即调用 `handle-media-callback` (或一个专门的错误处理函数) 更新数据库状态为 `failed` 并记录 `errorMessage`。
