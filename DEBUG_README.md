# 调试文件使用说明

## 📋 调试参数文件

已为各个云函数创建了调试参数文件，方便在uniCloud控制台或本地进行调试：

### 1. 语音识别调试
**文件**: `speech-recognition-whisper.param.json`
```json
{
	"taskId": "test-task-001",
	"audioOssUrl": "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/test-audio.mp3",
	"sourceLanguage": "en"
}
```

### 2. 翻译功能调试
**文件**: `subtitle-translation-gpt.param.json`
```json
{
	"taskId": "test-task-001",
	"sourceLanguage": "en",
	"targetLanguage": "zh"
}
```

### 3. 主流程调试
**文件**: `process-video-task.param.json`
- 包含多个测试场景的注释示例
- 默认测试语音识别功能

## 🧪 测试功能

### 测试任务支持
- 使用以 `test-` 开头的任务ID时，云函数会跳过数据库验证
- 自动使用模拟数据进行功能测试
- 方便在没有真实任务数据时进行功能验证

### 使用方法

1. **在uniCloud控制台调试**
   - 选择对应的云函数
   - 点击"运行"按钮
   - 参数会自动从 `.param.json` 文件加载

2. **修改测试参数**
   - 编辑对应的 `.param.json` 文件
   - 修改音频URL、语言设置等参数
   - 重新运行测试

3. **查看调试日志**
   - 在控制台查看详细的执行日志
   - 包含API调用过程和响应数据
   - 便于定位和解决问题

## 🔧 调试技巧

### 音频文件准备
- 确保音频文件可以通过HTTP访问
- 支持MP3、WAV等常见格式
- 建议使用较短的音频文件进行测试

### API配置检查
- 确保OpenAI API配置正确
- 检查API密钥和基础URL设置
- 验证网络连接和访问权限

### 错误排查
- 查看完整的错误日志
- 检查API响应格式
- 确认各项配置参数正确