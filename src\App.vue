<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";

onLaunch(() => {
  console.log("App Launch");

  // 设置状态栏样式
  uni.setNavigationBarColor({
    frontColor: '#ffffff',
    backgroundColor: '#4f46e5'
  });

  // 初始化全局配置
  initGlobalConfig();
});

onShow(() => {
  console.log("App Show");
});

onHide(() => {
  console.log("App Hide");
});

// 初始化全局配置
const initGlobalConfig = () => {
  // 设置全局字体
  const systemInfo = uni.getSystemInfoSync();
  console.log('系统信息:', systemInfo);

  // 根据设备类型调整样式
  if (systemInfo.platform === 'ios') {
    // iOS特定样式调整
    document.documentElement.style.setProperty('--safe-area-top', systemInfo.safeAreaInsets?.top + 'px' || '0px');
  }
};
</script>

<style>
/* 全局基础样式 - 兼容微信小程序 */
page {
  background-color: #fafafa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
}

/* 全局容器样式 */
view {
  box-sizing: border-box;
}

/* 全局输入框样式 */
input, textarea {
  box-sizing: border-box;
  border: none;
  outline: none;
  background: transparent;
}

/* 全局按钮样式 */
button {
  box-sizing: border-box;
  border: none;
  outline: none;
  background: transparent;
}

/* 全局图片样式 */
image {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 全局文字样式 */
text {
  word-wrap: break-word;
  word-break: break-all;
}

/* 全局动画性能优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* 全局触摸反馈 */
.touch-feedback {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* 全局加载状态 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.global-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
