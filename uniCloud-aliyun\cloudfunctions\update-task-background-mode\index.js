// uniCloud云函数：更新任务后台模式状态
"use strict";

/**
 * 更新任务的后台模式状态
 * 
 * @param {Object} event
 * @param {string} event.taskId - 任务ID（必需）
 * @param {boolean} event.backgroundMode - 是否为后台模式（必需）
 * @param {string} event.openid - 用户openid（可选，用于权限验证）
 * @returns {Object} 更新结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, backgroundMode, openid } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log("update-task-background-mode 云函数被调用，参数：", { 
      taskId, 
      backgroundMode, 
      hasOpenid: !!openid 
    });

    // ==================== 参数验证 ====================
    if (!taskId) {
      console.error("参数验证失败：缺少taskId参数");
      return {
        code: 400,
        message: "缺少必要参数：taskId",
        data: null,
      };
    }

    if (typeof backgroundMode !== 'boolean') {
      console.error("参数验证失败：backgroundMode必须是布尔值");
      return {
        code: 400,
        message: "backgroundMode参数必须是布尔值",
        data: null,
      };
    }

    // 验证taskId格式（简单验证）
    if (typeof taskId !== "string" || taskId.trim().length === 0) {
      console.error("参数验证失败：taskId格式不正确");
      return {
        code: 400,
        message: "taskId格式不正确",
        data: null,
      };
    }

    // ==================== 数据库操作 ====================
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 构建查询条件
    let taskQuery = tasksCollection.where({
      _id: taskId,
    });

    // 如果提供了openid，添加用户权限验证
    if (openid) {
      const usersCollection = db.collection("users");
      const userResult = await usersCollection
        .where({
          openid: openid,
        })
        .field({
          _id: true,
        })
        .limit(1)
        .get();

      if (!userResult.data || userResult.data.length === 0) {
        console.error("用户验证失败：用户不存在", openid);
        return {
          code: 401,
          message: "用户不存在或无权限",
          data: null,
        };
      }

      const userId = userResult.data[0]._id;
      console.log("用户验证通过，userId：", userId);

      // 添加用户权限过滤
      taskQuery = taskQuery.where({
        userId: userId,
      });
    }

    // 查询任务是否存在
    const taskResult = await taskQuery
      .field({
        _id: true,
        status: true,
        backgroundMode: true,
        userId: true,
        createTime: true,
        updateTime: true,
      })
      .limit(1)
      .get();

    if (!taskResult.data || taskResult.data.length === 0) {
      console.error("任务查询失败：任务不存在或无权限访问", taskId);
      return {
        code: 404,
        message: "任务不存在或无权限访问",
        data: null,
      };
    }

    const taskData = taskResult.data[0];
    console.log("任务查询成功，当前状态：", taskData.status, "当前后台模式：", taskData.backgroundMode);

    // ==================== 业务逻辑验证 ====================

    // 检查任务状态，只允许对进行中的任务修改后台模式
    const activeStatuses = [
      "uploading",
      "extracting_audio", 
      "recognizing",
      "translating",
      "merging"
    ];

    if (!activeStatuses.includes(taskData.status)) {
      console.log("任务状态不允许修改后台模式，当前状态：", taskData.status);
      return {
        code: 400,
        message: `任务状态为 ${taskData.status}，不允许修改后台模式`,
        data: {
          taskId: taskId,
          currentStatus: taskData.status,
          currentBackgroundMode: taskData.backgroundMode
        },
      };
    }

    // 如果状态没有变化，直接返回成功
    if (taskData.backgroundMode === backgroundMode) {
      console.log("后台模式状态无变化，直接返回成功");
      return {
        code: 200,
        message: "后台模式状态无变化",
        data: {
          taskId: taskId,
          backgroundMode: backgroundMode,
          status: taskData.status
        },
      };
    }

    // ==================== 更新数据库状态 ====================
    const updateTime = new Date();
    
    const updateResult = await tasksCollection.doc(taskId).update({
      backgroundMode: backgroundMode,
      updateTime: updateTime,
    });

    console.log("任务后台模式状态更新成功，更新记录数：", updateResult.updated);

    // ==================== 触发后台处理 ====================
    if (backgroundMode) {
      // 切换到后台模式时，触发task-scheduler立即处理该任务
      try {
        console.log("触发task-scheduler处理后台任务");
        const schedulerResult = await uniCloud.callFunction({
          name: 'task-scheduler',
          data: {
            taskId: taskId
          }
        });
        console.log("task-scheduler处理结果：", schedulerResult.result);
      } catch (schedulerError) {
        console.warn("触发task-scheduler失败，但不影响主流程：", schedulerError.message);
        // 不抛出错误，因为这不是关键操作
      }
    }

    // ==================== 返回结果 ====================
    return {
      code: 200,
      message: backgroundMode ? "任务已切换到后台模式" : "任务已切换到前台模式",
      data: {
        taskId: taskId,
        backgroundMode: backgroundMode,
        status: taskData.status,
        updatedAt: updateTime.toISOString(),
        previousBackgroundMode: taskData.backgroundMode,
      },
    };

  } catch (error) {
    console.error("update-task-background-mode 云函数执行错误：", error);

    return {
      code: 500,
      message: "服务器内部错误：" + error.message,
      data: null,
    };
  }
};
