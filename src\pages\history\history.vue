<template>
  <view class="history-container">
    <!-- 全页面下拉刷新滚动容器 -->
    <scroll-view
      class="page-scroll"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      refresher-default-style="black"
      refresher-background="#f0f4ff"
    >
      <!-- 现代化页面头部 -->
      <view class="page-header">
        <view class="header-background"></view>
        <view class="header-content">
          <view class="title-section">
            <text class="page-title">历史记录</text>
            <text class="page-subtitle">管理您的字幕任务</text>
          </view>
        </view>
      </view>

      <!-- 现代化搜索和筛选区域 - 仅在已登录时显示 -->
      <view v-if="userInfo.isLogin" class="search-filter-container">
        <!-- 搜索框 -->
        <view class="search-section">
          <view class="search-wrapper">
            <view class="search-icon-wrapper">
              <text class="search-icon">🔍</text>
            </view>
            <input
              class="search-input"
              type="text"
              placeholder="搜索文件名、任务ID..."
              v-model="searchKeyword"
              @input="onSearchInput"
            />
            <view v-if="searchKeyword" class="clear-button" @click="clearSearch">
              <text class="clear-icon">✕</text>
            </view>
          </view>
        </view>

        <!-- 筛选标签区域 -->
        <view class="filter-section">
          <view class="filter-header">
            <text class="filter-title">筛选</text>
            <text class="filter-count">{{ filteredTasks.length }} 个结果</text>
          </view>
          <scroll-view class="filter-scroll" scroll-x show-scrollbar="false">
            <view class="filter-tabs">
              <view
                class="filter-chip"
                :class="{ active: activeFilter === filter.value }"
                v-for="filter in filters"
                :key="filter.value"
                @click="setActiveFilter(filter.value)"
              >
                <text class="filter-label">{{ filter.label }}</text>
                <view v-if="filter.count !== undefined && filter.count > 0" class="filter-badge">
                  {{ filter.count }}
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 登录提示 - 仅在未登录时显示 -->
      <view v-if="!userInfo.isLogin" class="auth-section">
        <view class="auth-card">
          <view class="auth-visual">
            <view class="auth-icon">
              <text class="lock-icon">🔒</text>
              <view class="pulse-ring"></view>
            </view>
          </view>
          <view class="auth-content">
            <text class="auth-title">查看历史记录</text>
            <text class="auth-description">微信登录后即可查看和管理您的字幕任务</text>
            <view class="auth-features">
              <view class="feature-point">
                <text class="feature-icon">📝</text>
                <text class="feature-text">任务管理</text>
              </view>
              <view class="feature-point">
                <text class="feature-icon">📊</text>
                <text class="feature-text">处理记录</text>
              </view>
              <view class="feature-point">
                <text class="feature-icon">⏰</text>
                <text class="feature-text">历史追踪</text>
              </view>
            </view>
          </view>
          <button @click="goToLogin" class="auth-button">
            <text class="btn-text">微信一键登录</text>
            <view class="btn-arrow">→</view>
          </button>
        </view>
      </view>

      <!-- 历史记录列表 - 仅在已登录时显示 -->
      <view v-if="userInfo.isLogin" class="history-list">
        <!-- 加载状态 -->
        <view v-if="isLoading && tasks.length === 0" class="loading-state">
          <view class="loading-content">
            <view class="loading-spinner">
              <view class="spinner-ring"></view>
            </view>
            <text class="loading-text">正在加载历史记录...</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else-if="filteredTasks.length === 0 && !isLoading" class="empty-state">
          <view class="empty-content">
            <view class="empty-icon">📝</view>
            <text class="empty-title">{{ searchKeyword ? "未找到相关记录" : "暂无历史记录" }}</text>
            <text class="empty-desc">{{
              searchKeyword
                ? "尝试调整搜索关键词或筛选条件"
                : "开始上传视频，创建您的第一个字幕任务"
            }}</text>
            <button v-if="!searchKeyword" @click="goToUpload" class="empty-action-btn">
              <text class="btn-text">开始上传</text>
            </button>
            <button v-else @click="clearSearch" class="empty-action-btn secondary">
              <text class="btn-text">清除搜索</text>
            </button>
          </view>
        </view>

        <!-- 现代化任务列表 -->
        <view v-else class="tasks-container">
          <view
            class="task-item"
            v-for="(task, index) in filteredTasks"
            :key="task.taskId"
            @click="viewTaskDetail(task)"
            :style="{ 'animation-delay': index * 0.1 + 's' }"
          >
            <!-- 任务卡片主体 -->
            <view class="task-card">
              <!-- 左侧缩略图区域 -->
              <view class="task-media">
                <view class="media-container">
                  <image
                    v-if="task.thumbnailUrl"
                    :src="task.thumbnailUrl"
                    class="media-image"
                    mode="aspectFill"
                  />
                  <view v-else class="media-placeholder">
                    <text class="media-icon">🎬</text>
                  </view>

                  <!-- 状态徽章 -->
                  <view class="status-overlay">
                    <view class="status-badge" :class="getStatusClass(task.status)">
                      <!-- 处理中状态的加载图标 -->
                      <view v-if="isTaskProcessing(task.status)" class="status-loading-icon">
                        <view class="loading-spinner-small"></view>
                      </view>
                      <text class="status-text">{{ getStatusText(task.status) }}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 右侧内容区域 -->
              <view class="task-info">
                <!-- 任务标题和时间 -->
                <view class="info-header">
                  <text class="task-title">{{ task.fileName || "未知文件" }}</text>
                  <text class="task-timestamp">{{ formatTime(task.createdAt) }}</text>
                </view>

                <!-- 进度条 (仅处理中状态) -->
                <view v-if="isTaskProcessing(task.status)" class="progress-section">
                  <view
                    class="progress-container"
                    :class="{ 'processing-active': isTaskProcessing(task.status) }"
                  >
                    <view class="progress-track">
                      <view
                        class="progress-fill"
                        :style="{ width: (task.progress || 0) + '%' }"
                      ></view>
                      <!-- 处理中状态的动态光效 -->
                      <view v-if="isTaskProcessing(task.status)" class="progress-glow"></view>
                    </view>
                    <text class="progress-label">{{ task.progress || 0 }}%</text>
                    <!-- 处理状态指示器 -->
                    <view class="progress-status-indicator">
                      <view class="status-dot"></view>
                      <view class="status-dot status-dot-delay-1"></view>
                      <view class="status-dot status-dot-delay-2"></view>
                    </view>
                  </view>
                </view>

                <!-- 任务元信息 -->
                <view class="task-metadata">
                  <view class="metadata-item">
                    <text class="metadata-icon">📁</text>
                    <text class="metadata-value">{{ formatFileSize(task.fileSize) }}</text>
                  </view>
                  <view v-if="task.duration && task.duration > 0 && Number.isFinite(task.duration)" class="metadata-item">
                    <text class="metadata-icon">⏱️</text>
                    <text class="metadata-value">{{ formatDuration(task.duration) }}</text>
                  </view>
                  <view class="metadata-item">
                    <text class="metadata-icon">🆔</text>
                    <text class="metadata-value">{{ task.taskId.slice(-6) }}</text>
                  </view>
                </view>

                <!-- 操作按钮组 -->
                <view class="action-buttons" @click.stop>
                  <button
                    v-if="task.status === 'completed'"
                    @click="viewResult(task)"
                    class="action-button primary"
                  >
                    <text class="button-text">查看结果</text>
                  </button>
                  <button
                    v-else-if="canRetryTask(task)"
                    @click="retryTask(task.taskId)"
                    class="action-button warning"
                  >
                    <text class="button-text">重新处理</text>
                  </button>
                  <button
                    v-else-if="isTaskProcessing(task.status)"
                    @click="viewProgress(task)"
                    class="action-button info"
                  >
                    <text class="button-text">查看进度</text>
                  </button>
                  <button @click="deleteTask(task.taskId)" class="action-button danger">
                    <text class="button-text">删除</text>
                  </button>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 数据加载完成提示 -->
        <view v-if="!isLoading && tasks.length > 0" class="data-complete">
          <view class="complete-content">
            <text class="complete-icon">📊</text>
            <text class="complete-text">显示最近7天的 {{ tasks.length }} 条记录</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { onShow, onHide } from "@dcloudio/uni-app";
import { checkLoginStatus, handleLoginCheck, type LoginCheckResult } from "@/utils/common";

// 响应式数据
const searchKeyword = ref("");
const activeFilter = ref("all");
// 用户信息
const userInfo = ref({
  openid: "",
  isLogin: false,
});

// 任务数据
const tasks = ref<any[]>([]);
const isLoading = ref(false);
const isRefreshing = ref(false);

// 防抖定时器
let loadingTimeout: number | null = null;

// 轮询相关
const pollingInterval = ref<number | null>(null);
const isPolling = ref(false);
const POLLING_INTERVAL = 5000; // 5秒轮询间隔

// 计算各状态任务数量
const statusCounts = computed(() => {
  const counts: Record<string, number> = {
    all: tasks.value.length,
    processing: 0,
    completed: 0,
    failed: 0,
    cancelled: 0,
  };

  tasks.value.forEach((task) => {
    // 将所有处理状态归类为processing
    if (
      [
        "processing",
        "uploading",
        "extracting_audio",
        "recognizing",
        "translating",
        "merging",
      ].includes(task.status)
    ) {
      counts.processing++;
    } else if (task.status === "deleted") {
      // 已删除的任务不计入统计（软删除的任务通常不应该在前端显示）
      // 如果需要显示已删除任务，可以添加 deleted 状态到 counts 对象中
    } else if (counts.hasOwnProperty(task.status)) {
      counts[task.status]++;
    }
  });

  return counts;
});

// 筛选选项 - 动态显示数量
const filters = computed(() => [
  { label: "全部", value: "all", count: statusCounts.value.all },
  { label: "处理中", value: "processing", count: statusCounts.value.processing },
  { label: "已完成", value: "completed", count: statusCounts.value.completed },
  { label: "失败", value: "failed", count: statusCounts.value.failed },
  { label: "已取消", value: "cancelled", count: statusCounts.value.cancelled },
]);

// 计算过滤后的任务列表
const filteredTasks = computed(() => {
  let result = tasks.value;

  // 按状态筛选
  if (activeFilter.value !== "all") {
    if (activeFilter.value === "processing") {
      // "处理中"包含所有处理状态
      result = result.filter((task) =>
        [
          "processing",
          "uploading",
          "extracting_audio",
          "recognizing",
          "translating",
          "merging",
        ].includes(task.status)
      );
    } else {
      // 其他状态直接匹配
      result = result.filter((task) => task.status === activeFilter.value);
    }
  }

  // 过滤掉已删除的任务（软删除）
  result = result.filter((task) => task.status !== "deleted");

  // 按关键词搜索 - 支持文件名和任务ID搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(
      (task) =>
        (task.fileName && task.fileName.toLowerCase().includes(keyword)) ||
        (task.taskId && task.taskId.toLowerCase().includes(keyword))
    );
  }

  // 按时间倒序排列
  return result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
});

onMounted(async () => {
  // 加载用户信息
  await loadUserInfo();
  // 如果已登录，加载历史任务
  if (userInfo.value.isLogin) {
    await loadHistoryTasks();
    // 检查是否需要开始轮询
    checkAndStartPolling();
  }
});

// 页面卸载时清理定时器
onUnmounted(() => {
  clearPolling();
  if (loadingTimeout) {
    clearTimeout(loadingTimeout);
    loadingTimeout = null;
  }
});

// 页面显示时刷新数据
onShow(async () => {
  // 清除之前的防抖定时器
  if (loadingTimeout) {
    clearTimeout(loadingTimeout);
    loadingTimeout = null;
  }

  // 使用防抖机制，避免快速切换页面时重复调用
  loadingTimeout = setTimeout(async () => {
    console.log("历史页面显示，检查登录状态并刷新数据");
    try {
      // 使用统一的登录状态检查
      await checkAndUpdateLoginStatus();

      // 如果已登录，重新加载历史任务以获取最新数据
      if (userInfo.value.isLogin) {
        console.log("用户已登录，刷新历史任务数据");
        await loadHistoryTasks();
        // 页面重新激活时检查并启动轮询
        checkAndStartPolling();
      } else {
        console.log("用户未登录，跳过数据加载");
        // 用户未登录时停止轮询
        clearPolling();
        // 清空任务列表，避免显示之前的数据
        tasks.value = [];
      }
    } catch (error) {
      console.error("页面显示时刷新数据失败:", error);
    }
  }, 300); // 300ms 防抖延迟
});

// 页面隐藏时停止轮询
onHide(() => {
  console.log("历史页面隐藏，停止轮询");
  clearPolling();
});

// 加载用户信息（保持原有逻辑，用于页面初始化）
const loadUserInfo = async () => {
  try {
    // 从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync("userInfo");
    if (savedUserInfo && savedUserInfo.openid) {
      userInfo.value.openid = savedUserInfo.openid;
      userInfo.value.isLogin = true;
    }
  } catch (error) {
    console.error("加载用户信息失败:", error);
  }
};

// 检查并更新登录状态（用于onShow生命周期）
const checkAndUpdateLoginStatus = async () => {
  try {
    const loginCheck: LoginCheckResult = await checkLoginStatus();

    // 更新本地登录状态
    userInfo.value.isLogin = loginCheck.isLogin;
    userInfo.value.openid = loginCheck.openid || "";

    // 如果登录状态发生变化，处理相应逻辑
    if (!loginCheck.isLogin) {
      console.log("用户未登录或登录已过期:", loginCheck.reason);

      // 如果用户之前是登录状态但现在检查失败，显示提示
      if (loginCheck.needRelogin) {
        handleLoginCheck(loginCheck, {
          showToast: true,
          autoRedirect: false,
          customMessage: "登录已过期，请重新登录后查看历史记录",
        });
      }

      // 停止轮询和清空数据
      clearPolling();
      tasks.value = [];
    } else {
      console.log("用户登录状态正常，openid:", loginCheck.openid);
    }
  } catch (error) {
    console.error("检查登录状态失败:", error);
    // 发生错误时，保守处理，设置为未登录状态
    userInfo.value.isLogin = false;
    userInfo.value.openid = "";
    clearPolling();
    tasks.value = [];
  }
};

// 跳转到登录页面
const goToLogin = () => {
  uni.switchTab({
    url: "/pages/profile/profile",
  });
};

// 下拉刷新处理函数
const onRefresh = async () => {
  console.log("触发下拉刷新");

  // 检查用户是否已登录
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    console.log("用户未登录，无法刷新历史任务");
    isRefreshing.value = false;
    return;
  }

  try {
    isRefreshing.value = true;

    // 重新加载历史任务数据
    await loadHistoryTasks(true); // 传入 true 表示是刷新操作

    // 刷新后检查轮询状态
    checkAndStartPolling();
  } catch (error) {
    console.error("下拉刷新失败:", error);
    uni.showToast({
      title: "刷新失败",
      icon: "none",
      duration: 2000,
    });
  } finally {
    // 延迟结束刷新状态，确保用户能看到刷新动画
    setTimeout(() => {
      isRefreshing.value = false;
    }, 500);
  }
};

// 加载历史任务 - 查询最近7天的数据
const loadHistoryTasks = async (isRefreshAction = false) => {
  try {
    // 检查用户是否已登录
    if (!userInfo.value.isLogin || !userInfo.value.openid) {
      console.log("用户未登录，无法加载历史任务");
      return;
    }

    // 设置加载状态（刷新时不显示加载状态，避免与下拉刷新冲突）
    if (!isRefreshAction) {
      isLoading.value = true;
    }

    console.log("历史任务查询 - 用户openid:", userInfo.value.openid);

    // 计算7天前的日期
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    // 调用云函数获取最近7天的历史任务
    const result = await uniCloud.callFunction({
      name: "get-user-tasks",
      data: {
        openid: userInfo.value.openid,
        startDate: sevenDaysAgo.toISOString(),
      },
    });

    console.log("历史任务查询结果:", result);

    if (result.result.code === 200) {
      const newTasks = result.result.data || [];
      tasks.value = newTasks;
    } else {
      uni.showToast({
        title: result.result.message || "加载失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("加载历史任务失败:", error);
    uni.showToast({
      title: "加载失败",
      icon: "none",
    });
  } finally {
    // 只有非刷新操作才重置加载状态
    if (!isRefreshAction) {
      isLoading.value = false;
    }
  }
};

// ==================== 轮询相关函数 ====================

// 检查是否有处理中的任务
const hasProcessingTasks = (): boolean => {
  return tasks.value.some((task) => isTaskProcessing(task.status));
};

// 检查并启动轮询
const checkAndStartPolling = () => {
  if (hasProcessingTasks() && !isPolling.value) {
    console.log("检测到处理中的任务，启动轮询");
    startPolling();
  } else if (!hasProcessingTasks() && isPolling.value) {
    console.log("没有处理中的任务，停止轮询");
    clearPolling();
  }
};

// 启动轮询
const startPolling = () => {
  if (isPolling.value) {
    console.log("轮询已在运行中");
    return;
  }

  console.log("启动轮询，间隔:", POLLING_INTERVAL, "ms");
  isPolling.value = true;

  pollingInterval.value = setInterval(async () => {
    await pollTaskStatus();
  }, POLLING_INTERVAL);
};

// 清除轮询
const clearPolling = () => {
  if (pollingInterval.value) {
    console.log("清除轮询定时器");
    clearInterval(pollingInterval.value);
    pollingInterval.value = null;
  }
  isPolling.value = false;
};

// 轮询任务状态
const pollTaskStatus = async () => {
  // 检查用户是否仍然登录
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    console.log("用户未登录，停止轮询");
    clearPolling();
    return;
  }

  // 检查是否还有处理中的任务
  if (!hasProcessingTasks()) {
    console.log("没有处理中的任务，停止轮询");
    clearPolling();
    return;
  }

  console.log("轮询获取任务状态更新...");

  try {
    // 计算7天前的日期
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    // 调用云函数获取最新状态
    const result = await uniCloud.callFunction({
      name: "get-user-tasks",
      data: {
        openid: userInfo.value.openid,
        startDate: sevenDaysAgo.toISOString(),
      },
    });

    if (result.result.code === 200) {
      const newTasks = result.result.data || [];

      // 直接更新任务列表
      tasks.value = newTasks;

      // 检查是否还需要继续轮询
      if (!hasProcessingTasks()) {
        console.log("所有任务处理完成，停止轮询");
        clearPolling();
      }
    }
  } catch (error) {
    console.error("轮询请求失败:", error);
  }
};

// 设置活动筛选器 - 只进行前端筛选，不重新加载数据
const setActiveFilter = (filter: string) => {
  activeFilter.value = filter;
  // 筛选逻辑已在 filteredTasks 计算属性中处理，无需重新加载数据
};

// 搜索输入处理
const onSearchInput = () => {
  // 搜索是实时的，通过computed属性filteredTasks自动过滤
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = "";
};

// 查看结果
const viewResult = (task: any) => {
  uni.navigateTo({
    url: `/pages/result/result?taskId=${task.taskId}`,
  });
};

// 查看进度
const viewProgress = (task: any) => {
  uni.navigateTo({
    url: `/pages/process/process?taskId=${task.taskId}`,
  });
};

// 删除任务
const deleteTask = async (taskId: string) => {
  // 查找要删除的任务信息
  const taskToDelete = tasks.value.find((task) => task.taskId === taskId);
  if (!taskToDelete) {
    uni.showToast({
      title: "任务不存在",
      icon: "none",
    });
    return;
  }

  // 统一的删除确认提示，不区分任务状态
  const modalConfig = {
    title: "确认删除",
    content: `确定要删除任务"${taskToDelete.fileName || "未知文件"}"吗？删除后无法恢复。`,
    confirmText: "删除",
    confirmColor: "#ff4757",
    cancelText: "取消",
    success: async (res: any) => {
      if (res.confirm) {
        await performDeleteTask(taskId);
      }
    },
  };

  uni.showModal(modalConfig);
};

// 执行删除任务的具体操作
const performDeleteTask = async (taskId: string) => {
  try {
    uni.showLoading({
      title: "删除中...",
      mask: true,
    });

    console.log("开始删除任务:", taskId);

    const result = await uniCloud.callFunction({
      name: "delete-task",
      data: {
        taskId: taskId,
        openid: userInfo.value.openid,
      },
    });

    console.log("删除任务结果:", result);

    if (result.result.code === 200) {
      uni.hideLoading();

      // 显示成功提示
      uni.showToast({
        title: "删除成功",
        icon: "success",
        duration: 2000,
      });

      // 从本地列表中移除
      tasks.value = tasks.value.filter((task) => task.taskId !== taskId);

      console.log("任务删除成功，已从本地列表移除");

      // 删除后检查是否还需要轮询
      checkAndStartPolling();
    } else {
      uni.hideLoading();

      // 根据错误码显示不同的错误信息
      let errorMessage = "删除失败";
      switch (result.result.code) {
        case 401:
          errorMessage = "用户身份验证失败，请重新登录";
          break;
        case 403:
          errorMessage = "没有权限删除此任务";
          break;
        case 404:
          errorMessage = "任务不存在或已被删除";
          // 如果任务不存在，也从本地列表中移除
          tasks.value = tasks.value.filter((task) => task.taskId !== taskId);
          // 检查是否还需要轮询
          checkAndStartPolling();
          break;
        case 409:
          errorMessage = "任务正在处理中，请稍后重试";
          break;
        default:
          errorMessage = result.result.message || "删除失败，请稍后重试";
      }

      uni.showToast({
        title: errorMessage,
        icon: "none",
        duration: 3000,
      });
    }
  } catch (error) {
    console.error("删除任务失败:", error);
    uni.hideLoading();

    // 网络错误或其他异常
    uni.showToast({
      title: "网络错误，删除失败",
      icon: "none",
      duration: 3000,
    });
  }
};

// 查看任务详情
const viewTaskDetail = (task: any) => {
  if (task.status === "completed") {
    uni.navigateTo({
      url: `/pages/result/result?taskId=${task.taskId}`,
    });
  } else if (isTaskProcessing(task.status)) {
    // 所有处理中状态都跳转到进度页面
    uni.navigateTo({
      url: `/pages/process/process?taskId=${task.taskId}`,
    });
  } else if (["failed", "cancelled", "deleted"].includes(task.status)) {
    // 失败、已取消或已删除的任务
    if (canRetryTask(task)) {
      // 可以重新处理
      uni.showModal({
        title: "任务处理失败",
        content: "该任务处理失败，是否重新处理？",
        confirmText: "重新处理",
        confirmColor: "#f59e0b",
        success: (res: any) => {
          if (res.confirm) {
            retryTask(task.taskId);
          }
        },
      });
    } else {
      // 不能重新处理（通常是因为 ossUrl 为空）
      uni.showModal({
        title: "任务处理失败",
        content: "该任务处理失败，且原始视频文件不存在，无法重新处理。建议删除此任务。",
        confirmText: "删除任务",
        confirmColor: "#ff4757",
        cancelText: "取消",
        success: (res: any) => {
          if (res.confirm) {
            deleteTask(task.taskId);
          }
        },
      });
    }
  } else {
    // 其他未知状态
    uni.showToast({
      title: "任务状态异常",
      icon: "none",
    });
  }
};

// 检查任务是否可以重新处理
const canRetryTask = (task: any): boolean => {
  // 1. 任务状态必须是失败、已取消或已删除
  const retryableStatuses = ["failed", "cancelled", "deleted"];
  if (!retryableStatuses.includes(task.status)) {
    return false;
  }

  // 2. 必须有 ossUrl 字段且不为空
  if (!task.ossUrl || task.ossUrl.trim() === "") {
    return false;
  }

  return true;
};

// 检查任务是否正在处理中
const isTaskProcessing = (status: string): boolean => {
  const processingStatuses = [
    "uploading",
    "extracting_audio",
    "recognizing",
    "translating",
    "merging",
    "processing",
  ];
  return processingStatuses.includes(status);
};

// 重试任务
const retryTask = async (taskId: string) => {
  // 查找要重试的任务信息
  const taskToRetry = tasks.value.find((task) => task.taskId === taskId);
  if (!taskToRetry) {
    uni.showToast({
      title: "任务不存在",
      icon: "none",
    });
    return;
  }

  // 再次验证是否可以重试
  if (!canRetryTask(taskToRetry)) {
    let message = "该任务无法重新处理";
    if (!taskToRetry.ossUrl || taskToRetry.ossUrl.trim() === "") {
      message = "原始视频文件不存在，无法重新处理";
    } else if (!["failed", "cancelled", "deleted"].includes(taskToRetry.status)) {
      message = "只有失败或已取消的任务才能重新处理";
    }

    uni.showToast({
      title: message,
      icon: "none",
      duration: 3000,
    });
    return;
  }

  // 显示确认对话框
  uni.showModal({
    title: "确认重新处理",
    content: `确定要重新处理任务"${
      taskToRetry.fileName || "未知文件"
    }"吗？这将重新开始整个处理流程。`,
    confirmText: "重新处理",
    confirmColor: "#f59e0b",
    cancelText: "取消",
    success: async (res: any) => {
      if (res.confirm) {
        await performRetryTask(taskId);
      }
    },
  });
};

// 执行重试任务的具体操作
const performRetryTask = async (taskId: string) => {
  try {
    uni.showLoading({
      title: "启动重新处理...",
      mask: true,
    });

    console.log("开始重试任务:", taskId);

    // 调用云函数重试任务
    const result = await uniCloud.callFunction({
      name: "retry-task",
      data: {
        taskId: taskId,
        openid: userInfo.value.openid,
      },
    });

    console.log("重试任务结果:", result);

    if (result.result.code === 200) {
      uni.hideLoading();

      // 显示成功提示
      uni.showToast({
        title: "重新处理已启动",
        icon: "success",
        duration: 1500,
      });

      // 更新本地任务状态
      const taskIndex = tasks.value.findIndex((task) => task.taskId === taskId);
      if (taskIndex !== -1) {
        tasks.value[taskIndex].status = "uploading";
        tasks.value[taskIndex].errorMessage = "";
      }

      console.log("任务重试成功，即将跳转到进度页面");

      // 重试成功后启动轮询
      checkAndStartPolling();

      // 延迟跳转到进度页面，让用户看到成功提示
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/process/process?taskId=${taskId}`,
          fail: (error) => {
            console.error("跳转到进度页面失败:", error);
            // 如果跳转失败，刷新当前页面
            loadHistoryTasks();
          },
        });
      }, 1500);
    } else {
      uni.hideLoading();

      // 根据错误码显示不同的错误信息
      let errorMessage = "重新处理失败";
      switch (result.result.code) {
        case 401:
          errorMessage = "用户身份验证失败，请重新登录";
          break;
        case 403:
          errorMessage = "没有权限重新处理此任务";
          break;
        case 404:
          errorMessage = "任务不存在";
          break;
        case 409:
          errorMessage = "任务状态不允许重新处理";
          break;
        case 422:
          errorMessage = "原始视频文件不存在，无法重新处理";
          break;
        default:
          errorMessage = result.result.message || "重新处理失败，请稍后重试";
      }

      uni.showToast({
        title: errorMessage,
        icon: "none",
        duration: 3000,
      });
    }
  } catch (error) {
    console.error("重试任务失败:", error);
    uni.hideLoading();

    // 网络错误或其他异常
    uni.showToast({
      title: "网络错误，重新处理失败",
      icon: "none",
      duration: 3000,
    });
  }
};

// 跳转到上传页面
const goToUpload = () => {
  uni.switchTab({
    url: "/pages/upload/upload",
  });
};

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case "uploading":
      return "上传中";
    case "extracting_audio":
      return "提取音频";
    case "recognizing":
      return "语音识别";
    case "translating":
      return "生成字幕";
    case "merging":
      return "合成视频";
    case "processing":
      return "处理中";
    case "completed":
      return "已完成";
    case "failed":
      return "失败";
    case "cancelled":
      return "已取消";
    case "deleted":
      return "已删除";
    default:
      return "未知";
  }
};

// 获取状态样式类
const getStatusClass = (status: string): string => {
  switch (status) {
    case "uploading":
    case "extracting_audio":
    case "recognizing":
    case "translating":
    case "merging":
    case "processing":
      return "status-processing";
    case "completed":
      return "status-completed";
    case "failed":
      return "status-failed";
    case "cancelled":
    case "deleted":
      return "status-cancelled";
    default:
      return "status-unknown";
  }
};

// 格式化时间
const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  if (diff < 60000) return "刚刚";
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;

  return `${date.getMonth() + 1}-${date.getDate()}`;
};

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return "0B";
  if (size < 1024) return size + "B";
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + "KB";
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + "MB";
  return (size / (1024 * 1024 * 1024)).toFixed(1) + "GB";
};

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds || seconds <= 0 || !Number.isFinite(seconds)) return "00:00";
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
};
</script>

<style scoped>
/* ==================== 页面容器 ==================== */
.history-container {
  height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  display: flex;
  flex-direction: column;
}

/* 全页面滚动容器 */
.page-scroll {
  flex: 1;
  height: 100%;
  padding-bottom: calc(10rpx + env(safe-area-inset-bottom));
}

/* ==================== 现代化页面头部 ==================== */
.page-header {
  position: relative;
  padding: 40rpx 32rpx 32rpx;
  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 50%, #8b5cf6 100%);
  color: white;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 1;
}

.title-section {
  margin-bottom: 32rpx;
}

.page-title {
  font-size: 52rpx;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 8rpx;
  letter-spacing: -0.5rpx;
}

.page-subtitle {
  font-size: 30rpx;
  opacity: 0.9;
  font-weight: 500;
  letter-spacing: 0.2rpx;
  margin-left: 16rpx;
}

/* ==================== 未登录状态 ==================== */
.auth-section {
  margin-bottom: 40rpx;
  padding: 32rpx;
}

.auth-card {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 
    0 4rpx 24rpx rgba(0, 0, 0, 0.06),
    0 0 0 1rpx rgba(99, 102, 241, 0.1);
  position: relative;
  overflow: hidden;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
}

.auth-visual {
  text-align: center;
  margin-bottom: 32rpx;
}

.auth-icon {
  position: relative;
  display: inline-block;
}

.lock-icon {
  font-size: 64rpx;
  display: block;
  position: relative;
  z-index: 2;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  border: 2rpx solid #6366f1;
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-ring {
  0% { transform: translate(-50%, -50%) scale(0.5); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

.auth-content {
  text-align: center;
  margin-bottom: 40rpx;
}

.auth-title {
  display: block;
  font-size: 44rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.auth-description {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.auth-features {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  flex-wrap: wrap;
}

.feature-point {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.feature-icon {
  font-size: 24rpx;
}

.feature-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.auth-button {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border: none;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.auth-button:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(99, 102, 241, 0.4);
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.btn-arrow {
  font-size: 24rpx;
  color: white;
  transition: transform 0.3s ease;
}

.auth-button:active .btn-arrow {
  transform: translateX(4rpx);
}

/* ==================== 现代化搜索和筛选 ==================== */
.search-filter-container {
  margin: 16rpx 18rpx;
  background: white;
  border-radius: 28rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 搜索区域 */
.search-section {
  padding: 24rpx 24rpx 16rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.search-wrapper {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border-radius: 20rpx;
  padding: 0 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.search-wrapper:focus-within {
  border-color: #6366f1;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(99, 102, 241, 0.08);
}

.search-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #9ca3af;
  transition: all 0.3s ease;
}

.search-wrapper:focus-within .search-icon {
  color: #6366f1;
  transform: scale(1.1);
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  color: #1f2937;
  border: none;
  outline: none;
  background: transparent;
  font-weight: 500;
}

.search-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f1f5f9;
  margin-left: 10rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.clear-button:active {
  background: #e2e8f0;
  transform: scale(0.9);
}

.clear-icon {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 600;
}

/* 筛选区域 */
.filter-section {
  padding: 16rpx 24rpx 24rpx;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.filter-count {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.filter-scroll {
  width: 100%;
}

.filter-tabs {
  display: flex;
  gap: 12rpx;
  padding-bottom: 8rpx;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 14rpx 18rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.filter-chip::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.filter-chip:active::before {
  left: 100%;
}

.filter-chip.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-color: #6366f1;
  box-shadow: 0 6rpx 20rpx rgba(99, 102, 241, 0.3);
  transform: translateY(-2rpx);
}

.filter-label {
  font-size: 26rpx;
  font-weight: 600;
  color: inherit;
}

.filter-badge {
  background: rgba(255, 255, 255, 0.9);
  color: #6366f1;
  border-radius: 12rpx;
  padding: 4rpx 10rpx;
  font-size: 20rpx;
  font-weight: 700;
  min-width: 32rpx;
  text-align: center;
  line-height: 1.2;
}

.filter-chip.active .filter-badge {
  background: rgba(255, 255, 255, 0.25);
  color: white;
}

.filter-chip:not(.active) .filter-label {
  color: #6b7280;
}

.filter-chip:not(.active) .filter-badge {
  background: #e2e8f0;
  color: #64748b;
}

/* ==================== 历史列表 ==================== */
.history-list {
  box-sizing: border-box;
  padding: 0 18rpx;
  margin-bottom: 32rpx;
}

/* ==================== 加载状态 ==================== */
.loading-state {
  background: white;
  border-radius: 24rpx;
  padding: 80rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 32rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  position: relative;
  width: 80rpx;
  height: 80rpx;
}

.spinner-ring {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f4f6;
  border-top: 6rpx solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 30rpx;
  color: #6b7280;
  font-weight: 500;
}

/* ==================== 空状态 ==================== */
.empty-state {
  background: white;
  border-radius: 24rpx;
  padding: 80rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 32rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 16rpx;
  opacity: 0.8;
}

.empty-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.empty-desc {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 32rpx;
  max-width: 400rpx;
}

.empty-action-btn {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 24rpx 48rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-action-btn.secondary {
  background: #f8fafc;
  color: #6b7280;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.empty-action-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.4);
}

.empty-action-btn.secondary:active {
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
}

/* ==================== 现代化任务列表 ==================== */
.tasks-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.task-item {
  opacity: 0;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 任务卡片 */
.task-card {
  display: flex;
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.task-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.05), transparent);
  transition: left 0.6s;
}

.task-card:active {
  transform: translateY(-4rpx) scale(0.99);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  border-color: #6366f1;
}

.task-card:active::before {
  left: 100%;
}

/* 左侧媒体区域 */
.task-media {
  width: 120rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.media-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: #f8fafc;
}

.media-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.media-icon {
  font-size: 48rpx;
  color: #94a3b8;
  opacity: 0.8;
}

/* 状态覆盖层 */
.status-overlay {
  position: absolute;
  top: 22rpx;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-badge {
  display: flex;
  align-items: center;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  min-width: fit-content;
}

.status-badge.status-processing {
  background: rgba(59, 130, 246, 0.95);
  color: white;
  position: relative;
  overflow: hidden;
}

/* 状态徽章中的小型加载图标 */
.status-loading-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4rpx;
  flex-shrink: 0;
}

.loading-spinner-small {
  width: 14rpx;
  height: 14rpx;
  border: 1.5rpx solid rgba(255, 255, 255, 0.3);
  border-top: 1.5rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.status-badge.status-completed {
  background: rgba(34, 197, 94, 0.95);
  color: white;
}

.status-badge.status-failed {
  background: rgba(239, 68, 68, 0.95);
  color: white;
}

.status-badge.status-cancelled {
  background: rgba(107, 114, 128, 0.95);
  color: white;
}

.status-text {
  font-weight: 600;
  font-size: 20rpx;
  line-height: 1;
  flex-shrink: 0;
}

/* 右侧信息区域 */
.task-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120rpx;
}

/* 信息头部 */
.info-header {
  margin-bottom: 12rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.3;
  margin-bottom: 6rpx;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 410rpx;
}

.task-timestamp {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
  margin-left: 12rpx;
}

/* 进度区域 */
.progress-section {
  margin-bottom: 12rpx;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  border-radius: 16rpx;
  border: 1rpx solid #c7d2fe;
  position: relative;
  overflow: hidden;
}

.progress-container.processing-active {
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  border-color: #6366f1;
  box-shadow: 0 0 0 2rpx rgba(99, 102, 241, 0.1);
}

.progress-container.processing-active::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.progress-track {
  flex: 1;
  height: 6rpx;
  background: #e0e7ff;
  border-radius: 3rpx;
  overflow: hidden;
  position: relative;
}

/* 进度条光效 */
.progress-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
  animation: progress-glow 2s ease-in-out infinite;
}

@keyframes progress-glow {
  0%,
  100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 3rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.progress-label {
  font-size: 22rpx;
  color: #6366f1;
  font-weight: 700;
  min-width: 48rpx;
  text-align: right;
  position: relative;
  z-index: 1;
}

/* 进度状态指示器 */
.progress-status-indicator {
  display: flex;
  align-items: center;
  gap: 4rpx;
  margin-left: 8rpx;
}

.status-dot {
  width: 6rpx;
  height: 6rpx;
  background: #6366f1;
  border-radius: 50%;
  animation: status-pulse 1.5s ease-in-out infinite;
}

.status-dot-delay-1 {
  animation-delay: 0.3s;
}

.status-dot-delay-2 {
  animation-delay: 0.6s;
}

@keyframes status-pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 任务元数据 */
.task-metadata {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.metadata-icon {
  font-size: 18rpx;
  opacity: 0.8;
}

.metadata-value {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.action-button {
  flex: 1;
  min-width: 100rpx;
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.action-button:active::before {
  left: 100%;
}

.action-button.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.25);
}

.action-button.primary:active {
  transform: translateY(-2rpx) scale(0.97);
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.35);
}

.action-button.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(245, 158, 11, 0.25);
}

.action-button.warning:active {
  transform: translateY(-2rpx) scale(0.97);
  box-shadow: 0 8rpx 24rpx rgba(245, 158, 11, 0.35);
}

.action-button.info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.25);
}

.action-button.info:active {
  transform: translateY(-2rpx) scale(0.97);
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.35);
}

.action-button.danger {
  background: #fee2e2;
  color: #dc2626;
  border: 1rpx solid #fecaca;
  box-shadow: 0 2rpx 12rpx rgba(220, 38, 38, 0.15);
}

.action-button.danger:active {
  background: #fecaca;
  transform: translateY(-2rpx) scale(0.97);
  box-shadow: 0 6rpx 20rpx rgba(220, 38, 38, 0.25);
}

.button-text {
  font-weight: 600;
  position: relative;
  z-index: 1;
  font-size: 24rpx;
}

/* ==================== 数据完成提示 ==================== */
.data-complete {
  margin: 32rpx 0;
  text-align: center;
}

.complete-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 20rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.complete-icon {
  font-size: 32rpx;
  opacity: 0.8;
}

.complete-text {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 500;
}

/* ==================== 响应式适配 ==================== */
/* 小屏幕优化 */
@media (max-width: 375px) {
  .page-header {
    padding: 32rpx 24rpx 24rpx;
  }

  .page-title {
    font-size: 44rpx;
  }

  .search-filter-container {
    margin: 20rpx 24rpx;
  }

  .tasks-container {
    padding: 0 24rpx;
    gap: 16rpx;
  }

  .task-card {
    padding: 20rpx;
  }

  .task-media {
    width: 100rpx;
    margin-right: 16rpx;
  }

  .media-container {
    width: 100rpx;
    height: 100rpx;
  }

  .task-title {
    font-size: 28rpx;
  }

  .action-button {
    padding: 10rpx 12rpx;
    font-size: 22rpx;
    min-width: 80rpx;
  }
}

/* ==================== 高级动画效果 ==================== */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    transform: translate3d(0, -8rpx, 0);
  }

  70% {
    transform: translate3d(0, -4rpx, 0);
  }

  90% {
    transform: translate3d(0, -2rpx, 0);
  }
}

.action-button:active {
  animation: bounce 0.6s;
}
</style>
