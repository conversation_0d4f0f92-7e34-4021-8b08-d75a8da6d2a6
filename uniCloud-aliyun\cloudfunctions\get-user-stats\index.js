// uniCloud云函数：获取用户统计数据
"use strict";

/**
 * 获取用户统计数据
 * 专门用于个人中心页面的统计信息展示
 *
 * @param {Object} event
 * @param {string} event.openid - 用户openid（必需）
 * @param {string} event.timeRange - 统计时间范围（可选：'7d', '30d', '90d', 'all'，默认'all'）
 * @returns {Object} 用户统计数据
 */
exports.main = async (event, context) => {
  try {
    const { openid, timeRange = 'all' } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log("get-user-stats 云函数被调用，参数：", { 
      openid: openid ? openid.substring(0, 8) + '***' : null, 
      timeRange 
    });

    // 参数验证
    if (!openid) {
      console.error("参数验证失败：缺少openid参数");
      return {
        errCode: "INVALID_PARAM",
        errMsg: "缺少必要参数：openid",
        code: 400
      };
    }

    // 验证时间范围参数
    const validTimeRanges = ['7d', '30d', '90d', 'all'];
    if (!validTimeRanges.includes(timeRange)) {
      console.error("参数验证失败：无效的时间范围", timeRange);
      return {
        errCode: "INVALID_TIME_RANGE",
        errMsg: "无效的时间范围参数",
        code: 400
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const usersCollection = db.collection("users");
    const tasksCollection = db.collection("tasks");

    // 查询用户信息
    console.log("开始查询用户信息...");
    const userResult = await usersCollection
      .where({ openid: openid })
      .field({
        _id: true,
        createTime: true,
        lastActiveTime: true,
        lastLoginTime: true,
        nickname: true,
        avatar: true,
        status: true
      })
      .limit(1)
      .get();

    if (!userResult.data || userResult.data.length === 0) {
      console.error("用户验证失败：用户不存在", openid);
      return {
        errCode: "USER_NOT_FOUND",
        errMsg: "用户不存在",
        code: 404
      };
    }

    const userData = userResult.data[0];
    const userId = userData._id;
    console.log("用户验证通过，userId：", userId);

    // 计算查询时间范围
    let queryStartDate = null;
    if (timeRange !== 'all') {
      const days = parseInt(timeRange.replace('d', ''));
      queryStartDate = new Date();
      queryStartDate.setDate(queryStartDate.getDate() - days);
      queryStartDate.setHours(0, 0, 0, 0); // 设置为当天开始
    }

    console.log("查询时间范围：", queryStartDate ? queryStartDate.toISOString() : '全部时间', "至今");

    // 构建查询条件
    let queryCondition = { userId: userId };
    if (queryStartDate) {
      queryCondition.createTime = db.command.gte(queryStartDate);
    }

    // 查询用户任务数据
    console.log("开始查询用户任务数据...");
    const tasksResult = await tasksCollection
      .where(queryCondition)
      .field({
        _id: true,
        status: true,
        createTime: true,
        updateTime: true,
        fileSize: true,
        duration: true,
        fileName: true
      })
      .orderBy('createTime', 'desc')
      .get();

    const tasks = tasksResult.data || [];
    console.log(`查询完成，找到 ${tasks.length} 条任务记录`);

    // 计算统计数据
    const statistics = calculateDetailedStatistics(tasks, userData, timeRange);

    // 更新用户最后活跃时间
    try {
      await usersCollection
        .where({ _id: userId })
        .update({
          lastActiveTime: new Date(),
          lastActiveIP: CLIENTIP,
          lastActiveUA: CLIENTUA
        });
    } catch (updateError) {
      console.warn("更新用户活跃时间失败：", updateError.message);
    }

    return {
      errCode: 0,
      code: 200,
      errMsg: "查询成功",
      data: statistics
    };

  } catch (error) {
    console.error("get-user-stats 云函数执行错误：", error);

    return {
      errCode: "GET_USER_STATS_FAILED",
      code: 500,
      errMsg: "获取用户统计数据失败: " + error.message,
    };
  }
};

/**
 * 计算详细的用户统计信息
 * @param {Array} tasks 任务列表
 * @param {Object} userData 用户数据
 * @param {string} timeRange 时间范围
 * @returns {Object} 详细统计信息
 */
function calculateDetailedStatistics(tasks, userData, timeRange) {
  // 基础统计
  const stats = {
    // 任务统计
    totalTasks: tasks.length,
    completedTasks: 0,
    failedTasks: 0,
    cancelledTasks: 0, // 前端期望的字段，对应 failed 状态
    processingTasks: 0,
    
    // 数据统计
    totalDuration: 0,      // 总视频时长（秒）
    totalSize: 0,          // 总文件大小（字节）
    averageProcessTime: 0, // 平均处理时间（秒）
    
    // 效率统计
    successRate: 0,        // 成功率（百分比）
    averageFileSize: 0,    // 平均文件大小（字节）
    averageDuration: 0,    // 平均视频时长（秒）
    
    // 用户信息
    memberSince: userData.createTime,
    lastActiveTime: userData.lastActiveTime,
    timeRange: timeRange,
    
    // 状态分布
    statusDistribution: {
      uploading: 0,
      extracting_audio: 0,
      recognizing: 0,
      translating: 0,
      merging: 0,
      completed: 0,
      failed: 0
    }
  };

  let totalProcessingTimeMs = 0;
  let completedTasksWithTime = 0;

  // 遍历任务进行统计
  tasks.forEach(task => {
    // 统计任务状态
    switch (task.status) {
      case 'completed':
        stats.completedTasks++;
        stats.statusDistribution.completed++;
        break;
      case 'failed':
        stats.failedTasks++;
        stats.cancelledTasks++; // 前端将 failed 视为 cancelled
        stats.statusDistribution.failed++;
        break;
      case 'uploading':
        stats.processingTasks++;
        stats.statusDistribution.uploading++;
        break;
      case 'extracting_audio':
        stats.processingTasks++;
        stats.statusDistribution.extracting_audio++;
        break;
      case 'recognizing':
        stats.processingTasks++;
        stats.statusDistribution.recognizing++;
        break;
      case 'translating':
        stats.processingTasks++;
        stats.statusDistribution.translating++;
        break;
      case 'merging':
        stats.processingTasks++;
        stats.statusDistribution.merging++;
        break;
    }

    // 累计文件大小和时长
    if (task.fileSize && task.fileSize > 0) {
      stats.totalSize += task.fileSize;
    }
    if (task.duration && task.duration > 0) {
      stats.totalDuration += task.duration;
    }

    // 计算处理时间（仅对已完成的任务）
    if (task.status === 'completed' && task.createTime && task.updateTime) {
      const processingTime = new Date(task.updateTime) - new Date(task.createTime);
      if (processingTime > 0) {
        totalProcessingTimeMs += processingTime;
        completedTasksWithTime++;
      }
    }
  });

  // 计算平均值和比率
  if (stats.totalTasks > 0) {
    // 成功率
    stats.successRate = Math.round((stats.completedTasks / stats.totalTasks) * 100);
    
    // 平均文件大小
    if (stats.totalSize > 0) {
      stats.averageFileSize = Math.round(stats.totalSize / stats.totalTasks);
    }
    
    // 平均视频时长
    if (stats.totalDuration > 0) {
      stats.averageDuration = Math.round(stats.totalDuration / stats.totalTasks);
    }
  }

  // 平均处理时间（秒）
  if (completedTasksWithTime > 0) {
    stats.averageProcessTime = Math.round(totalProcessingTimeMs / completedTasksWithTime / 1000);
  }

  console.log("统计计算完成：", {
    totalTasks: stats.totalTasks,
    completedTasks: stats.completedTasks,
    failedTasks: stats.failedTasks,
    successRate: stats.successRate,
    totalDuration: stats.totalDuration,
    totalSize: stats.totalSize
  });

  return stats;
}
